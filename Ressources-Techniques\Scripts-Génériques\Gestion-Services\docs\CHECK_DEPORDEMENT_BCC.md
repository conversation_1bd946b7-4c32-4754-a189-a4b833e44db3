# Check_depordement.ksh - Destinataires en copie cachée (BCC)

## Vue d'ensemble

Le script `Check_depordement.ksh` supporte maintenant les destinataires en copie cachée (BCC) pour une gestion flexible et confidentielle des notifications email.

## Fonctionnalité BCC

### 🔒 Principe des destinataires BCC
- **TO (Destinataires principaux)** : Visibles par tous les destinataires
- **BCC (Copie cachée)** : Invisibles entre eux, reçoivent une copie
- **Confidentialité** : Les destinataires BCC ne voient pas les autres destinataires BCC
- **Flexibilité** : Possibilité d'utiliser TO seul, BCC seul, ou TO + BCC

## Configuration

### 📧 Variables d'email étendues
| Variable | Description | Exemple |
|----------|-------------|---------|
| `EMAIL_DEST` | Destinataires principaux (TO) | `"<EMAIL>,<EMAIL>"` |
| `EMAIL_BCC` | Destinataires en copie cachée | `"<EMAIL>,<EMAIL>"` |
| `EMAIL_SUBJECT` | Sujet de l'email | `"[URGENT] Problème de paie détecté"` |
| `SEND_EMAIL` | Force l'envoi (optionnel) | `1` |

### 🎯 Exemples de configuration

#### Configuration standard (TO + BCC)
```bash
export EMAIL_DEST="<EMAIL>,<EMAIL>"
export EMAIL_BCC="<EMAIL>,<EMAIL>"
./Check_depordement.ksh FICHIER.log
```

#### BCC uniquement
```bash
export EMAIL_BCC="<EMAIL>,<EMAIL>"
./Check_depordement.ksh FICHIER.log
```

#### Configuration par environnement
```bash
# Développement
export EMAIL_DEST="<EMAIL>"
export EMAIL_BCC="<EMAIL>"

# Test
export EMAIL_DEST="<EMAIL>"
export EMAIL_BCC="<EMAIL>,<EMAIL>"

# Production
export EMAIL_DEST="<EMAIL>,<EMAIL>"
export EMAIL_BCC="<EMAIL>,<EMAIL>,<EMAIL>"
```

## Avantages des destinataires BCC

### ✅ Confidentialité
- **Protection des adresses** : Les destinataires BCC ne voient pas les autres destinataires BCC
- **Évite les réponses à tous** : Pas de risque de réponse involontaire à tous les destinataires
- **Séparation des rôles** : Direction informée sans être exposée

### ✅ Organisation
- **Destinataires opérationnels (TO)** : Équipes devant agir immédiatement
- **Destinataires informatifs (BCC)** : Management, audit, archivage
- **Hiérarchie respectée** : Information sans ingérence

### ✅ Flexibilité
- **Configuration adaptable** : Selon l'environnement et les besoins
- **Escalade possible** : Ajout de destinataires selon la gravité
- **Archivage automatique** : Inclusion d'adresses d'archivage

### ✅ Conformité
- **Traçabilité** : Archivage automatique via BCC
- **Audit** : Équipes de compliance informées
- **Documentation** : Historique complet des notifications

## Cas d'usage recommandés

### 🏢 Environnement d'entreprise

#### Destinataires principaux (TO)
- **Équipes opérationnelles** : <EMAIL>, <EMAIL>
- **Responsables techniques** : <EMAIL>
- **Personnes devant agir** : <EMAIL>

#### Destinataires BCC
- **Direction** : <EMAIL>, <EMAIL>
- **Management** : <EMAIL>, <EMAIL>
- **Audit et compliance** : <EMAIL>, <EMAIL>
- **Archivage** : <EMAIL>, <EMAIL>
- **Monitoring** : <EMAIL>, <EMAIL>

### 📊 Matrice de distribution

| Gravité | TO (Action requise) | BCC (Information) |
|---------|-------------------|------------------|
| **🟢 Normal** | <EMAIL> | <EMAIL> |
| **🟡 Attention** | <EMAIL>, <EMAIL> | <EMAIL>, <EMAIL> |
| **🔴 Critique** | <EMAIL>, <EMAIL>, <EMAIL> | <EMAIL>, <EMAIL>, <EMAIL> |

## Sortie du script avec BCC

### 📊 Configuration affichée
```
Configuration email :
  Envoi automatique        : OUI
  Destinataires (TO)       : <EMAIL>,<EMAIL>
  Destinataires (BCC)      : <EMAIL>,<EMAIL>
  Sujet                    : Rapport d'analyse de paie
```

### ✅ Envoi réussi
```
>>> Envoi d'email automatique
=============================
Destinataires (TO)  : <EMAIL>,<EMAIL>
Destinataires (BCC) : <EMAIL>,<EMAIL>
Sujet               : Rapport d'analyse de paie
✅ Email envoyé avec succès
   Destinataires (TO)  : <EMAIL>,<EMAIL>
   Destinataires (BCC) : <EMAIL>,<EMAIL>
   Taille              : 3247 caractères
```

### 🔒 BCC uniquement
```
>>> Envoi d'email automatique
=============================
Destinataires (TO)  : Aucun
Destinataires (BCC) : <EMAIL>,<EMAIL>
Sujet               : Rapport d'analyse de paie
✅ Email envoyé avec succès
   Destinataires (TO)  : noreply@hostname
   Destinataires (BCC) : <EMAIL>,<EMAIL>
   Taille              : 3247 caractères
```

### ℹ️ Email non envoyé (aucun problème)
```
>>> Email configuré mais non envoyé
==================================
Destinataires (TO)  : <EMAIL>
Destinataires (BCC) : <EMAIL>,<EMAIL>
Raison              : Aucun problème détecté (pas de dépassement ni débordement)
ℹ️  L'email n'est envoyé qu'en cas de problème
```

## Logging avec BCC

### 📝 Log du script
```
[2023-12-15 14:30:25] Configuration - EMAIL_DEST: <EMAIL>
[2023-12-15 14:30:25] Configuration - EMAIL_BCC: <EMAIL>,<EMAIL>
[2023-12-15 14:30:37] Tentative d'envoi email - Problèmes détectés: Dépassements=2, Débordements=0
[2023-12-15 14:30:38] Email envoyé avec succès - TO: <EMAIL>, BCC: <EMAIL>,<EMAIL>
```

### 📧 Log des emails
```
[2023-12-15 14:30:37] TENTATIVE_ENVOI - TO: <EMAIL>, BCC: <EMAIL>,<EMAIL>, Sujet: Rapport d'analyse de paie
[2023-12-15 14:30:38] ENVOI_REUSSI - TO: <EMAIL>, BCC: <EMAIL>,<EMAIL>, Taille: 3247 caractères
# Contenu de l'email envoyé le 2023-12-15 14:30:38
# Destinataires (TO): <EMAIL>
# Destinataires (BCC): <EMAIL>,<EMAIL>
# Sujet: Rapport d'analyse de paie
# Taille: 3247 caractères
# ===========================================
[... contenu complet de l'email ...]
# ===========================================
```

## Exemples d'utilisation avancée

### 🔄 Script avec escalade automatique
```bash
#!/bin/bash
# Escalade selon la gravité avec BCC

# Configuration de base
export EMAIL_DEST="<EMAIL>"
export EMAIL_BCC="<EMAIL>"

# Analyse
./Check_depordement.ksh "$1"

# Vérifier la gravité dans les logs
LOG_TODAY="$LOG/Check_depordement_$(date +%Y%m%d).log"

if grep -q "Problème systémique probable" "$LOG_TODAY"; then
    echo "🔴 ESCALADE : Problème critique détecté"
    
    # Escalade avec BCC étendu
    export EMAIL_DEST="<EMAIL>,<EMAIL>"
    export EMAIL_BCC="<EMAIL>,<EMAIL>,<EMAIL>"
    export EMAIL_SUBJECT="[CRITIQUE] Problème systémique de paie - Intervention urgente"
    
    # Renvoyer avec escalade
    LATEST_EMAIL=$(ls -t /tmp/email_content_*.txt | head -1)
    if [ -f "$LATEST_EMAIL" ]; then
        MAIL_CMD="mail -s \"$EMAIL_SUBJECT\""
        if [ -n "$EMAIL_BCC" ]; then
            MAIL_CMD="$MAIL_CMD -b \"$EMAIL_BCC\""
        fi
        MAIL_CMD="$MAIL_CMD \"$EMAIL_DEST\""
        eval "$MAIL_CMD < \"$LATEST_EMAIL\""
        echo "✅ Escalade envoyée avec BCC étendu"
    fi
fi
```

### 📊 Script avec notification différenciée
```bash
#!/bin/bash
# Notifications différenciées selon le type de problème

LOG_FILE="$1"

# Analyse
./Check_depordement.ksh "$LOG_FILE"

# Vérifier le type de problème dans les logs
LOG_TODAY="$LOG/Check_depordement_$(date +%Y%m%d).log"

if grep -q "Débordements détectés" "$LOG_TODAY"; then
    echo "💾 Débordements détectés - Notification RH"
    
    # Configuration spécifique pour débordements
    export EMAIL_DEST="<EMAIL>"
    export EMAIL_BCC="<EMAIL>,<EMAIL>"
    export EMAIL_SUBJECT="[RH] Débordements de paie détectés"
    
elif grep -q "Dépassements détectés" "$LOG_TODAY"; then
    echo "🔧 Dépassements détectés - Notification technique"
    
    # Configuration spécifique pour dépassements
    export EMAIL_DEST="<EMAIL>,<EMAIL>"
    export EMAIL_BCC="<EMAIL>"
    export EMAIL_SUBJECT="[TECH] Dépassements de capacité détectés"
fi
```

### 🗄️ Script avec archivage automatique
```bash
#!/bin/bash
# Archivage automatique avec BCC

# Configuration avec archivage
export EMAIL_DEST="<EMAIL>"
export EMAIL_BCC="<EMAIL>,<EMAIL>"
export EMAIL_SUBJECT="Rapport de paie - $(date +%d/%m/%Y)"

# Analyse
./Check_depordement.ksh "$1"

# Les emails sont automatiquement archivés via BCC
echo "📧 Email envoyé avec archivage automatique via BCC"
```

## Implémentation technique

### 🔧 Commande mail générée
```bash
# Avec TO et BCC
mail -s "Sujet" -b "<EMAIL>,<EMAIL>" "<EMAIL>,<EMAIL>"

# BCC uniquement (utilise un expéditeur factice)
mail -s "Sujet" -b "<EMAIL>,<EMAIL>" "noreply@hostname"
```

### 📋 Logique de construction
1. **Vérification** des destinataires (TO ou BCC définis)
2. **Construction** de la commande mail avec options
3. **Ajout BCC** si défini (`-b "destinataires"`)
4. **Destinataire factice** si BCC seul (`noreply@hostname`)
5. **Exécution** et logging du résultat

## Dépannage

### ❌ Problèmes courants

#### Email non reçu par destinataires BCC
- **Vérifier** la syntaxe : `EMAIL_BCC="<EMAIL>,<EMAIL>"`
- **Tester** la commande mail : `echo "test" | mail -s "test" -b "<EMAIL>" "<EMAIL>"`
- **Consulter** les logs mail du système

#### Destinataires BCC visibles
- **Problème** : Configuration mail incorrecte
- **Solution** : Vérifier la configuration du serveur SMTP
- **Test** : Utiliser un client mail pour vérifier les en-têtes

#### Erreur de syntaxe mail
- **Problème** : Caractères spéciaux dans les adresses
- **Solution** : Échapper les caractères ou utiliser des guillemets
- **Exemple** : `EMAIL_BCC="\"<EMAIL>\""`

### 🔍 Diagnostic
```bash
# Vérifier la configuration
echo "EMAIL_DEST: $EMAIL_DEST"
echo "EMAIL_BCC: $EMAIL_BCC"

# Tester la commande mail
echo "Test BCC" | mail -s "Test" -b "<EMAIL>" "<EMAIL>"

# Vérifier les logs
tail -f $LOG/Check_depordement_emails_$(date +%Y%m%d).log
```

---

**Documentation BCC** - Check_depordement.ksh  
**Équipe TechOps** - $(date +%Y-%m-%d)
