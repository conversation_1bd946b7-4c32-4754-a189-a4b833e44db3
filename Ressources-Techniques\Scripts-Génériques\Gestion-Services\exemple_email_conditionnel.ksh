#!/bin/ksh
#===============================================================================
# Exemples d'envoi d'email conditionnel - Check_depordement.ksh
#===============================================================================
# Description : Email envoyé SEULEMENT si dépassements ou débordements détectés
# Version     : 1.0
#===============================================================================

echo "==============================================================================="
echo "Exemples d'envoi d'email conditionnel - Check_depordement.ksh"
echo "==============================================================================="
echo ""

echo "🎯 PRINCIPE : EMAIL SEULEMENT SI PROBLÈMES DÉTECTÉS"
echo "=================================================="
echo ""
echo "Le script n'envoie un email que si :"
echo "  ✅ Dépassements de capacité détectés (BBAD0012)"
echo "  ✅ Débordements d'informations détectés"
echo "  ❌ AUCUN email si tout va bien (évite le spam)"
echo ""

echo "📊 TABLEAU DE COMPORTEMENT :"
echo "============================"
echo ""
echo "| Situation                | Dépassements | Débordements | Email envoyé |"
echo "|--------------------------|--------------|--------------|--------------|"
echo "| Tout va bien             |      0       |      0       |   ❌ Non     |"
echo "| Dépassements seulement   |     > 0      |      0       |   ✅ Oui     |"
echo "| Débordements seulement   |      0       |     > 0      |   ✅ Oui     |"
echo "| Les deux problèmes       |     > 0      |     > 0      |   ✅ Oui     |"
echo ""

echo "1. CONFIGURATION STANDARD :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   ./Check_depordement.ksh FICHIER.log"
echo ""
echo "   → Si problèmes : Email envoyé automatiquement"
echo "   → Si tout va bien : Message informatif, pas d'email"
echo ""

echo "2. SURVEILLANCE QUOTIDIENNE INTELLIGENTE :"
cat << 'EOF'
   #!/bin/bash
   # Script de surveillance quotidienne sans spam
   
   export EMAIL_DEST="<EMAIL>"
   export EMAIL_SUBJECT="[ALERTE] Problème de paie détecté - $(date +%d/%m/%Y)"
   
   for log_file in /var/log/paie/FFCALK3W.*; do
       if [ -f "$log_file" ]; then
           echo "Analyse de $(basename "$log_file")"
           ./Check_depordement.ksh "$(basename "$log_file")"
           # Email envoyé automatiquement SEULEMENT si problème
       fi
   done
   
   echo "Surveillance terminée - Emails envoyés uniquement en cas de problème"
EOF
echo ""

echo "3. SCRIPT AVEC DIFFÉRENTS NIVEAUX D'ALERTE :"
cat << 'EOF'
   #!/bin/bash
   # Alertes différenciées selon la gravité
   
   LOG_FILE="$1"
   
   # Configuration email de base
   export EMAIL_DEST="<EMAIL>"
   
   # Analyse
   ./Check_depordement.ksh "$LOG_FILE"
   RESULT=$?
   
   if [ $RESULT -eq 0 ]; then
       echo "✅ Aucun problème détecté - Pas d'email envoyé"
   else
       echo "⚠️  Problèmes détectés - Email d'alerte envoyé"
       
       # Email supplémentaire pour les cas critiques
       if grep -q "DEBORDEMENT" /tmp/email_content_*.txt 2>/dev/null; then
           echo "🔴 Débordements critiques détectés"
           # Envoyer un SMS ou notification urgente si nécessaire
       fi
   fi
EOF
echo ""

echo "4. SCRIPT AVEC ESCALADE AUTOMATIQUE :"
cat << 'EOF'
   #!/bin/bash
   # Escalade selon le nombre de problèmes
   
   export EMAIL_DEST="<EMAIL>"
   
   # Analyse avec seuils personnalisés
   export SEUIL_DEPASSEMENTS=1
   export SEUIL_DEBORDEMENTS=1
   
   ./Check_depordement.ksh "$1"
   
   # Vérifier le niveau de gravité dans le contenu email
   if [ -f /tmp/email_content_*.txt ]; then
       EMAIL_FILE=$(ls -t /tmp/email_content_*.txt | head -1)
       
       # Compter les problèmes
       DEPASSEMENTS=$(grep -o "détecté(s)" "$EMAIL_FILE" | wc -l)
       
       if [ "$DEPASSEMENTS" -gt 10 ]; then
           echo "🔴 CRITIQUE : Plus de 10 problèmes détectés"
           # Escalade vers le management
           export EMAIL_DEST="<EMAIL>,<EMAIL>,<EMAIL>"
           export EMAIL_SUBJECT="[CRITIQUE] Problème majeur de paie - Intervention requise"
           
           # Renvoyer avec escalade
           mail -s "$EMAIL_SUBJECT" "$EMAIL_DEST" < "$EMAIL_FILE"
       fi
   fi
EOF
echo ""

echo "5. SCRIPT AVEC HISTORIQUE DES ALERTES :"
cat << 'EOF'
   #!/bin/bash
   # Suivi des alertes avec historique
   
   ALERT_LOG="/var/log/paie_alerts.log"
   DATE=$(date +'%Y-%m-%d %H:%M:%S')
   
   export EMAIL_DEST="<EMAIL>"
   
   # Analyse
   ./Check_depordement.ksh "$1"
   RESULT=$?
   
   if [ $RESULT -eq 0 ]; then
       echo "$DATE - OK - Aucun problème détecté dans $1" >> "$ALERT_LOG"
   else
       echo "$DATE - ALERTE - Problèmes détectés dans $1 - Email envoyé" >> "$ALERT_LOG"
       
       # Vérifier la fréquence des alertes
       ALERTS_TODAY=$(grep "$(date +%Y-%m-%d)" "$ALERT_LOG" | grep -c "ALERTE")
       
       if [ "$ALERTS_TODAY" -gt 5 ]; then
           echo "⚠️  $ALERTS_TODAY alertes aujourd'hui - Problème récurrent possible"
       fi
   fi
EOF
echo ""

echo "==============================================================================="
echo "Exemples de sortie selon les cas :"
echo "==============================================================================="
echo ""

echo "CAS 1 - AUCUN PROBLÈME DÉTECTÉ :"
echo "================================"
echo ""
echo "Configuration des filtres :"
echo "  Seuil dépassements       : 1"
echo "  Seuil débordements       : 1"
echo ""
echo "Configuration email :"
echo "  Envoi automatique        : OUI"
echo "  Destinataires            : <EMAIL>"
echo "  Sujet                    : Rapport d'analyse de paie"
echo ""
echo ">>> Analyse terminée"
echo "Statut final : ✅ SUCCÈS - Aucun problème critique détecté"
echo "Recommandation : Surveillance préventive recommandée"
echo ""
echo ">>> Email configuré mais non envoyé"
echo "=================================="
echo "Destinataires : <EMAIL>"
echo "Raison        : Aucun problème détecté (pas de dépassement ni débordement)"
echo "ℹ️  L'email n'est envoyé qu'en cas de problème"
echo ""

echo "CAS 2 - PROBLÈMES DÉTECTÉS :"
echo "============================"
echo ""
echo "Configuration email :"
echo "  Envoi automatique        : OUI"
echo "  Destinataires            : <EMAIL>"
echo "  Sujet                    : Rapport d'analyse de paie"
echo ""
echo ">>> Analyse terminée"
echo "Statut final : ⚠️  ATTENTION - Problèmes détectés nécessitant surveillance"
echo "Recommandation : Vérifier les dépassements et surveiller les prochains traitements"
echo ""
echo ">>> Envoi d'email automatique"
echo "============================="
echo "Destinataires : <EMAIL>"
echo "Sujet         : Rapport d'analyse de paie"
echo "✅ Email envoyé avec succès"
echo "   Destinataires : <EMAIL>"
echo "   Taille : 3247 caractères"
echo ""

echo "CAS 3 - ERREUR D'ENVOI :"
echo "========================"
echo ""
echo ">>> Envoi d'email automatique"
echo "============================="
echo "Destinataires : <EMAIL>"
echo "Sujet         : Rapport d'analyse de paie"
echo "❌ Erreur lors de l'envoi de l'email"
echo "   Vérifier la configuration du serveur mail"
echo "   Contenu sauvegardé dans : /tmp/email_content_12345.txt"
echo ""

echo "==============================================================================="
echo "Avantages de l'envoi conditionnel :"
echo "==============================================================================="
echo ""

echo "✅ ÉVITE LE SPAM :"
echo "=================="
echo "• Pas d'email quotidien si tout va bien"
echo "• Réduction de la fatigue d'alerte"
echo "• Focus sur les vrais problèmes"
echo "• Boîte mail moins encombrée"
echo ""

echo "✅ ALERTE EFFICACE :"
echo "==================="
echo "• Email = problème réel détecté"
echo "• Réaction immédiate nécessaire"
echo "• Pas de faux positifs"
echo "• Confiance dans les alertes"
echo ""

echo "✅ SURVEILLANCE INTELLIGENTE :"
echo "=============================="
echo "• Monitoring silencieux si OK"
echo "• Alerte bruyante si problème"
echo "• Escalade possible selon gravité"
echo "• Historique des incidents"
echo ""

echo "✅ CONFORMITÉ OPÉRATIONNELLE :"
echo "=============================="
echo "• Respect des bonnes pratiques"
echo "• Réduction de la charge cognitive"
echo "• Amélioration de la réactivité"
echo "• Documentation automatique des incidents"
echo ""

echo "==============================================================================="
echo "Configuration recommandée pour différents environnements :"
echo "==============================================================================="
echo ""

echo "🔧 ENVIRONNEMENT DE DÉVELOPPEMENT :"
echo "==================================="
echo "export EMAIL_DEST=\"<EMAIL>\""
echo "export EMAIL_SUBJECT=\"[DEV] Problème détecté en développement\""
echo "export SEUIL_DEPASSEMENTS=5  # Plus tolérant"
echo "export SEUIL_DEBORDEMENTS=3"
echo ""

echo "🔧 ENVIRONNEMENT DE TEST :"
echo "=========================="
echo "export EMAIL_DEST=\"<EMAIL>\""
echo "export EMAIL_SUBJECT=\"[TEST] Problème détecté en test\""
echo "export SEUIL_DEPASSEMENTS=3"
echo "export SEUIL_DEBORDEMENTS=2"
echo ""

echo "🔧 ENVIRONNEMENT DE PRODUCTION :"
echo "================================"
echo "export EMAIL_DEST=\"<EMAIL>,<EMAIL>\""
echo "export EMAIL_SUBJECT=\"[PROD] ALERTE - Problème de paie détecté\""
echo "export SEUIL_DEPASSEMENTS=1  # Très strict"
echo "export SEUIL_DEBORDEMENTS=1"
echo ""

echo "==============================================================================="
echo "Test de la fonctionnalité :"
echo "==============================================================================="
echo ""
echo "Pour tester le comportement conditionnel :"
echo ""
echo "1. Test avec fichier sans problème :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   ./Check_depordement.ksh FICHIER_OK.log"
echo "   → Doit afficher 'Email configuré mais non envoyé'"
echo ""
echo "2. Test avec fichier contenant des problèmes :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   ./Check_depordement.ksh FICHIER_PROBLEME.log"
echo "   → Doit envoyer un email"
echo ""
echo "3. Vérifier la réception :"
echo "   → Email reçu seulement pour le cas 2"
echo "   → Pas d'email pour le cas 1"
echo ""
echo "==============================================================================="
