# Check_depordement.ksh - Version Simple avec Email

## Vue d'ensemble

Le script `Check_depordement.ksh` a été simplifié pour offrir une utilisation directe et efficace avec envoi d'email automatique. Toutes les fonctionnalités complexes (PDF, XLSX, CSV, Perl, Python) ont été supprimées pour se concentrer sur l'essentiel.

## Fonctionnalités

### ✅ Ce qui est conservé
- **Analyse complète** des logs de paie
- **Détection** des dépassements de capacité (BBAD0012)
- **Détection** des débordements d'informations
- **Filtrage avancé** (codes, matricules, seuils)
- **Sortie claire** et professionnelle
- **Envoi d'email** automatique avec rapport détaillé

### ❌ Ce qui a été supprimé
- Génération de fichiers PDF
- Génération de fichiers Excel XLSX
- Génération de fichiers CSV
- Dépendances Python/Perl
- Code complexe de formatage

## Utilisation

### Utilisation de base
```bash
# Analyse simple
./Check_depordement.ksh FICHIER.log

# Analyse avec email
export EMAIL_DEST="<EMAIL>"
./Check_depordement.ksh FICHIER.log
```

### Configuration email
```bash
# Un destinataire
export EMAIL_DEST="<EMAIL>"

# Plusieurs destinataires
export EMAIL_DEST="<EMAIL>,<EMAIL>,<EMAIL>"

# Sujet personnalisé
export EMAIL_SUBJECT="[URGENT] Problème de paie détecté"

# Envoi automatique si EMAIL_DEST défini
export SEND_EMAIL=1
```

### Avec filtrage
```bash
# Filtres + email
export IGNORE_CODES="TEST,DEMO"
export SEUIL_DEPASSEMENTS=5
export EMAIL_DEST="<EMAIL>"
./Check_depordement.ksh FICHIER.log
```

## Contenu de l'email

### Structure du rapport email
```
===============================================================================
RAPPORT D'ANALYSE DES DÉPASSEMENTS DE CAPACITÉ
===============================================================================
Fichier analysé : FFCALK3W_RJ2.2925
Date d'analyse  : 15/12/2023 à 14:30:25
Durée d'analyse : 12s
Environnement   : prod

RÉSUMÉ EXÉCUTIF :
================
Statut : ✅ SUCCÈS - Aucun problème critique détecté
Recommandation : Surveillance préventive recommandée

STATISTIQUES :
==============
• Période de paie        : 202312
• Type de paie           : Mensuelle
• Dépassements détectés  : 2
• Débordements détectés  : 0
• Erreurs générales      : 0
• Avertissements         : 1
• Total lignes analysées : 15847
```

### Si problèmes détectés
```
DÉTAILS DES PROBLÈMES :
======================

🔍 DÉPASSEMENTS DE CAPACITÉ (2 détecté(s)) :
---------------------------------------------------
Codes d'information concernés :
  - ZYWA (1 occurrence(s))
  - ABCD (1 occurrence(s))

Exemples de dépassements (5 premiers) :
  [Lignes exactes du log avec détails techniques]

💾 MESSAGES DE DÉBORDEMENT (0 détecté(s)) :
--------------------------------------------------------
Matricules concernés (0 au total) :
  [Liste des matricules si applicable]

ACTIONS RECOMMANDÉES :
=====================
🔴 URGENT :
  • Vérifier le traitement de paie pour les matricules listés
  • Contrôler la complétude des bulletins de paie
  • Prévoir un retraitement si nécessaire

🔧 TECHNIQUE :
  • Augmenter l'espace de stockage
  • Optimiser les paramètres de capacité
  • Surveiller les prochains traitements
```

### Si aucun problème
```
✅ AUCUN PROBLÈME DÉTECTÉ
=========================
Le traitement de paie s'est déroulé sans dépassement ni débordement.
```

## Prérequis

### Commande mail
```bash
# Installation
apt-get install mailutils    # Debian/Ubuntu
yum install mailx            # CentOS/RHEL

# Vérification
command -v mail
echo "Test" | mail -s "Test" <EMAIL>
```

### Configuration SMTP
- Serveur SMTP configuré sur le système
- Paramètres de relais corrects
- Adresses email valides

## Exemples d'utilisation

### Script de surveillance quotidienne
```bash
#!/bin/bash
# Surveillance automatique simple

export EMAIL_DEST="<EMAIL>"
export EMAIL_SUBJECT="Rapport quotidien de paie - $(date +%d/%m/%Y)"

for log_file in /var/log/paie/FFCALK3W.*; do
    if [ -f "$log_file" ]; then
        echo "Analyse de $(basename "$log_file")"
        ./Check_depordement.ksh "$(basename "$log_file")"
    fi
done
```

### Script avec gestion d'erreur
```bash
#!/bin/bash
# Script robuste

LOG_FILE="$1"

if [ -z "$LOG_FILE" ] || [ ! -f "$LOG_FILE" ]; then
    echo "Usage: $0 FICHIER.log"
    exit 1
fi

export EMAIL_DEST="<EMAIL>"
export EMAIL_SUBJECT="Analyse de paie - $(basename "$LOG_FILE")"

./Check_depordement.ksh "$LOG_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Analyse terminée avec succès"
else
    echo "⚠️  Problèmes détectés - Vérifier l'email"
fi
```

### Script avec filtres personnalisés
```bash
#!/bin/bash
# Filtrage avancé

export IGNORE_CODES="TEST,DEMO"
export SEUIL_DEPASSEMENTS=5
export SEUIL_DEBORDEMENTS=3
export EMAIL_DEST="<EMAIL>"
export EMAIL_SUBJECT="Rapport filtré de paie"

./Check_depordement.ksh "$1"
```

## Sortie du script

### Sans email
```
>>> Analyse terminée
Statut final : ✅ SUCCÈS - Aucun problème critique détecté
Recommandation : Surveillance préventive recommandée
```

### Avec email activé
```
>>> Envoi d'email automatique
=============================
Destinataires : <EMAIL>
Sujet         : Rapport d'analyse de paie
✅ Email envoyé avec succès
   Destinataires : <EMAIL>
   Taille : 2847 caractères
```

### En cas d'erreur
```
>>> Envoi d'email automatique
=============================
❌ Commande 'mail' non disponible
   Installation : apt-get install mailutils (Debian/Ubuntu)
   ou           : yum install mailx (CentOS/RHEL)
```

ou

```
❌ Erreur lors de l'envoi de l'email
   Vérifier la configuration du serveur mail
   Contenu sauvegardé dans : /tmp/email_content_12345.txt
```

## Variables de configuration

### Email
| Variable | Description | Exemple |
|----------|-------------|---------|
| `EMAIL_DEST` | Destinataires (séparés par virgules) | `"<EMAIL>,<EMAIL>"` |
| `EMAIL_SUBJECT` | Sujet de l'email | `"[URGENT] Problème de paie"` |
| `SEND_EMAIL` | Force l'envoi (optionnel si EMAIL_DEST défini) | `1` |

### Filtrage (inchangé)
| Variable | Description | Défaut |
|----------|-------------|--------|
| `IGNORE_CODES` | Codes à ignorer | `""` |
| `IGNORE_MATRICULES` | Matricules à ignorer | `""` |
| `SEUIL_DEPASSEMENTS` | Seuil minimum dépassements | `1` |
| `SEUIL_DEBORDEMENTS` | Seuil minimum débordements | `1` |

## Dépannage

### Email non reçu
1. **Vérifier la commande mail** : `command -v mail`
2. **Tester l'envoi** : `echo "Test" | mail -s "Test" <EMAIL>`
3. **Contrôler les logs** : `/var/log/mail.log`
4. **Vérifier la configuration SMTP** du système

### Erreur d'envoi
1. **Contenu sauvegardé** dans `/tmp/email_content_*.txt`
2. **Vérifier les permissions** d'écriture
3. **Contrôler l'espace disque** disponible
4. **Tester la configuration** mail du système

### Commande mail manquante
```bash
# Installation selon la distribution
apt-get install mailutils    # Debian/Ubuntu
yum install mailx            # CentOS/RHEL
```

## Migration depuis version complexe

### Changements principaux
- ✅ **Suppression** de toutes les dépendances externes
- ✅ **Simplification** de l'utilisation
- ✅ **Conservation** de toute la logique d'analyse
- ✅ **Amélioration** du rapport email

### Scripts existants
```bash
# Ancien (avec rapports)
export GENERATE_PDF=1
export GENERATE_XLSX=1
./Check_depordement.ksh FICHIER.log

# Nouveau (simple avec email)
export EMAIL_DEST="<EMAIL>"
./Check_depordement.ksh FICHIER.log
```

### Avantages de la simplification
- **Aucune dépendance** externe (Python, Perl, modules)
- **Installation** immédiate sur tout système Unix/Linux
- **Maintenance** simplifiée
- **Performance** améliorée
- **Fiabilité** accrue
- **Utilisation** plus intuitive

## Test rapide

```bash
# 1. Vérifier les prérequis
command -v mail

# 2. Test d'envoi
echo "Test" | mail -s "Test" <EMAIL>

# 3. Analyse simple
./Check_depordement.ksh FICHIER.log

# 4. Analyse avec email
export EMAIL_DEST="<EMAIL>"
./Check_depordement.ksh FICHIER.log

# 5. Vérifier la réception
```

---

**Version Simple** - Analyse efficace avec email automatique  
**Équipe TechOps** - $(date +%Y-%m-%d)
