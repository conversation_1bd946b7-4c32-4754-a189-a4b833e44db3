#!/bin/ksh
#===============================================================================
# Script de génération de rapport email professionnel
#===============================================================================
# Description : Génère un rapport email professionnel pour les anomalies de paie
# Auteur      : Équipe TechOps
# Version     : 1.0
# Date        : $(date +%Y-%m-%d)
#
# Paramètres :
#   $1 : Fichier de log à analyser
#   $2 : Environnement (prod, test, etc.)
#   $3 : Nombre de jours d'historique (optionnel, défaut: 1)
#
# Variables d'environnement requises :
#   LOG, TMP, EMAIL_DEST
#
# Utilisation :
#   ./generate_email_report.ksh FFCALK3W.10070 prod 1
#===============================================================================

# Configuration
LOG_FILE="$1"
ENVIRONNEMENT="${2:-prod}"
NB_JOURS="${3:-1}"
DATE_RAPPORT=$(date +'%d/%m/%Y à %H:%M')

# Validation des paramètres
if [ -z "$LOG_FILE" ]; then
    echo "ERREUR: Fichier de log manquant" >&2
    echo "Usage: $0 <FICHIER_LOG> [ENVIRONNEMENT] [NB_JOURS]" >&2
    exit 1
fi

# Fichiers temporaires
RAPPORT_TEMP="$TMP/rapport_email_$$.txt"
SUJET_TEMP="$TMP/sujet_email_$$.txt"

# Génération du rapport avec le script d'analyse
./Check_depordement.ksh "$LOG_FILE" > "$RAPPORT_TEMP"
STATUS_ANALYSE=$?

# Génération de l'email professionnel
{
    echo "Objet: Rapport de traitement de paie - Environnement $ENVIRONNEMENT"
    echo ""
    echo "Bonjour,"
    echo ""
    echo "Voici le rapport automatique du traitement de paie pour l'environnement $ENVIRONNEMENT."
    echo "Date du rapport : $DATE_RAPPORT"
    echo "Période analysée : $NB_JOURS dernier(s) jour(s)"
    echo ""
    echo "???????????????????????????????????????????????????????????????????????????????"
    echo "RAPPORT D'ANALYSE"
    echo "???????????????????????????????????????????????????????????????????????????????"
    echo ""
    
    # Contenu du rapport d'analyse
    cat "$RAPPORT_TEMP"
    
    echo ""
    echo "???????????????????????????????????????????????????????????????????????????????"
    echo "INFORMATIONS TECHNIQUES"
    echo "???????????????????????????????????????????????????????????????????????????????"
    echo ""
    echo "Fichier analysé : $LOG_FILE"
    echo "Environnement   : $ENVIRONNEMENT"
    echo "Date d'analyse  : $DATE_RAPPORT"
    echo ""
    
    if [ "$STATUS_ANALYSE" -ne 0 ]; then
        echo "??  Des anomalies ont été détectées dans le traitement."
        echo "Une analyse technique détaillée est disponible sur demande."
    else
        echo "? Le traitement s'est déroulé sans anomalie."
    fi
    
    echo ""
    echo "???????????????????????????????????????????????????????????????????????????????"
    echo ""
    echo "Ce rapport est généré automatiquement."
    echo "Pour toute question ou assistance, contactez l'équipe support technique."
    echo ""
    echo "Cordialement,"
    echo "Système de surveillance automatique"
    
} > "${RAPPORT_TEMP}_final"

# Génération du sujet selon le statut
if [ "$STATUS_ANALYSE" -ne 0 ]; then
    echo "?? ATTENTION - Anomalies détectées - Environnement $ENVIRONNEMENT" > "$SUJET_TEMP"
else
    echo "? Traitement nominal - Environnement $ENVIRONNEMENT" > "$SUJET_TEMP"
fi

# Envoi de l'email si EMAIL_DEST est défini
if [ -n "$EMAIL_DEST" ]; then
    SUJET=$(cat "$SUJET_TEMP")
    
    # Envoi via mail (adapter selon votre système de messagerie)
    mail -s "$SUJET" "$EMAIL_DEST" < "${RAPPORT_TEMP}_final"
    
    echo "Email envoyé à : $EMAIL_DEST"
    echo "Sujet : $SUJET"
else
    echo "Variable EMAIL_DEST non définie - Rapport généré dans : ${RAPPORT_TEMP}_final"
    echo "Sujet suggéré : $(cat "$SUJET_TEMP")"
fi

# Affichage du rapport pour vérification
echo ""
echo "???????????????????????????????????????????????????????????????????????????????"
echo "APERÇU DU RAPPORT GÉNÉRÉ :"
echo "???????????????????????????????????????????????????????????????????????????????"
cat "${RAPPORT_TEMP}_final"

# Nettoyage
rm -f "$RAPPORT_TEMP" "$SUJET_TEMP" "${RAPPORT_TEMP}_final"

exit $STATUS_ANALYSE
