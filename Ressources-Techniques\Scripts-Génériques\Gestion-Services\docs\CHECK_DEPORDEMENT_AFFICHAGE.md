# Check_depordement.ksh - Affichage détaillé des dépassements et débordements

## Vue d'ensemble

Le script `Check_depordement.ksh` affiche maintenant chaque dépassement avec son débordement associé dans un format visuel clair et structuré, facilitant l'analyse et le diagnostic.

## Nouvel affichage visuel

### 🎯 Principe
- **Chaque dépassement** est affiché dans un cadre distinct
- **Association immédiate** avec son débordement
- **Codes couleur** pour le statut (🔴🟡⚪)
- **Séparation claire** entre les incidents

### 📊 Exemple d'affichage détaillé

```
>>> Analyse détaillée : Dépassements et Débordements associés
==============================================================

┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔍 DÉPASSEMENT #1                                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1247                                                  │
│ Code d'information  : ZYWA                                                  │
│ Table concernée     : TABLE ZYWA                                            │
│ Message complet     : BBAD0012-DEPASSEMENT DE CAPACITE ZYWA...              │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│ ➡️  DÉBORDEMENT ASSOCIÉ                                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1249                                                  │
│ Matricule concerné  : ABC123456                                             │
│ Écart avec dépass.  : 2 ligne(s)                                            │
│ Message complet     : NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT...        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📋 ASSOCIATION DÉTECTÉE :                                                   │
│    Dépassement Code ZYWA → Débordement Matricule ABC123456                 │
│    Impact : Données non traitées pour ce matricule                         │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│ ⚠️  DÉBORDEMENT ISOLÉ #1                                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1789                                                  │
│ Matricule concerné  : GHI345678                                             │
│ Message complet     : NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT...        │
│ Note                : Aucun dépassement associé dans les 10 lignes précéd. │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│ 📊 RÉSUMÉ DE L'ANALYSE DÉTAILLÉE                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ Dépassements détectés        : 2                                            │
│ Débordements associés        : 1                                            │
│ Débordements isolés          : 1                                            │
│ Total débordements           : 2                                            │
│ Taux d'association           : 50%                                          │
│ Évaluation                   : 🟡 Problème récurrent à surveiller           │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Tableau récapitulatif

### 📋 Format du tableau
```
>>> Tableau récapitulatif des associations Dépassement → Débordement
======================================================================
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│ Code Info       │ Matricule       │ Ligne Dépass.   │ Ligne Débord.   │ Statut          │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ ZYWA            │ ABC123456       │ 1247            │ 1249            │ 🔴 Associé      │
│ ABCD            │ DEF789012       │ 1456            │ 1458            │ 🔴 Associé      │
│ WXYZ            │ JKL901234       │ 1678            │ 1685            │ 🟡 Probable     │
│ N/A             │ GHI345678       │ N/A             │ 1789            │ ⚪ Isolé        │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

### 📊 Colonnes du tableau

| Colonne | Description | Exemple |
|---------|-------------|---------|
| **Code Info** | Code d'information du dépassement | ZYWA, ABCD, N/A |
| **Matricule** | Matricule concerné par le débordement | ABC123456 |
| **Ligne Dépass.** | Numéro de ligne du dépassement | 1247, N/A |
| **Ligne Débord.** | Numéro de ligne du débordement | 1249 |
| **Statut** | Niveau de confiance de l'association | 🔴🟡⚪ |

## Statuts d'association

### 🔴 Associé (écart ≤ 3 lignes)
- **Forte probabilité** de lien causal
- **Dépassement → Débordement** direct
- **Action corrective** ciblée possible
- **Priorité** : Haute

### 🟡 Probable (écart 4-10 lignes)
- **Probabilité moyenne** de lien
- **Peut être lié** ou coïncidence
- **Investigation supplémentaire** recommandée
- **Priorité** : Moyenne

### ⚪ Isolé (aucun dépassement proche)
- **Débordement** sans dépassement associé
- **Cause différente** ou problème distinct
- **Analyse spécifique** nécessaire
- **Priorité** : Variable selon contexte

## Évaluation automatique de la gravité

### 🎯 Calcul du taux d'association
```
Taux = (Débordements associés / Dépassements détectés) × 100
```

### 📊 Niveaux d'évaluation

| Taux d'association | Évaluation | Icône | Action recommandée |
|-------------------|------------|-------|-------------------|
| **≥ 80%** | Problème systémique probable | 🔴 | Action corrective urgente |
| **≥ 50%** | Problème récurrent à surveiller | 🟡 | Surveillance renforcée |
| **< 50%** | Incidents ponctuels | 🟢 | Monitoring normal |

### 🔍 Interprétation

#### 🔴 Problème systémique (≥ 80%)
- **Cause structurelle** probable
- **Impact généralisé** sur le traitement
- **Intervention technique** urgente requise
- **Escalade** vers l'équipe technique

#### 🟡 Problème récurrent (≥ 50%)
- **Tendance** à surveiller
- **Optimisation** des paramètres recommandée
- **Monitoring** renforcé nécessaire
- **Planification** d'actions préventives

#### 🟢 Incidents ponctuels (< 50%)
- **Cas isolés** ou aléatoires
- **Surveillance** normale suffisante
- **Documentation** pour historique
- **Pas d'action** immédiate requise

## Avantages de l'affichage détaillé

### ✅ Clarté visuelle
- **Cadres distincts** pour chaque incident
- **Association immédiate** visible
- **Codes couleur** intuitifs
- **Séparation claire** des informations

### ✅ Informations complètes
- **Numéro de ligne** exact dans le log
- **Code d'information** du dépassement
- **Matricule concerné** par le débordement
- **Écart temporel** entre les événements
- **Impact métier** expliqué

### ✅ Analyse facilitée
- **Identification rapide** des associations
- **Détection** des débordements isolés
- **Évaluation automatique** de la gravité
- **Vue d'ensemble** avec tableau récapitulatif

### ✅ Diagnostic amélioré
- **Taux d'association** calculé automatiquement
- **Évaluation** du type de problème
- **Priorisation** des actions
- **Documentation** automatique

## Utilisation pratique

### 👨‍💼 Pour les administrateurs
- **Identification rapide** des problèmes critiques
- **Priorisation** des actions correctives
- **Suivi** des matricules impactés
- **Escalade** basée sur l'évaluation

### 👥 Pour les équipes métier
- **Compréhension immédiate** de l'impact
- **Liste** des employés à vérifier
- **Évaluation** de la gravité du problème
- **Actions** à entreprendre clairement définies

### 🛠️ Pour le support technique
- **Diagnostic facilité** avec numéros de ligne
- **Corrélation claire** cause → effet
- **Documentation automatique** des incidents
- **Traçabilité** complète des problèmes

## Intégration avec les autres fonctionnalités

### 📧 Email automatique
Le contenu détaillé est inclus dans l'email :
- **Résumé exécutif** avec évaluation
- **Exemples** de dépassements (5 premiers)
- **Liste** des matricules (10 premiers)
- **Actions recommandées** selon la gravité

### 📝 Logging complet
Toutes les informations sont loggées :
- **Nombre** de dépassements et débordements
- **Taux d'association** calculé
- **Évaluation** de la gravité
- **Durée** d'analyse détaillée

### 🔍 Filtrage avancé
L'affichage respecte les filtres :
- **Codes ignorés** non affichés
- **Matricules ignorés** masqués
- **Seuils** appliqués correctement
- **Configuration** documentée

## Exemples de cas d'usage

### 📊 Cas 1 : Problème systémique
```
Dépassements détectés        : 5
Débordements associés        : 5
Taux d'association           : 100%
Évaluation                   : 🔴 Problème systémique probable

→ Action : Intervention technique urgente
```

### 📊 Cas 2 : Problème récurrent
```
Dépassements détectés        : 4
Débordements associés        : 2
Taux d'association           : 50%
Évaluation                   : 🟡 Problème récurrent à surveiller

→ Action : Surveillance renforcée
```

### 📊 Cas 3 : Incidents ponctuels
```
Dépassements détectés        : 6
Débordements associés        : 1
Taux d'association           : 17%
Évaluation                   : 🟢 Incidents ponctuels

→ Action : Monitoring normal
```

## Configuration et personnalisation

### 🔧 Variables d'affichage
Le script utilise des variables internes pour l'affichage :
- **Seuil d'association** : 10 lignes maximum
- **Seuil "Associé"** : ≤ 3 lignes
- **Seuil "Probable"** : 4-10 lignes
- **Largeur des cadres** : 79 caractères

### 📏 Adaptation à l'environnement
L'affichage s'adapte automatiquement :
- **Terminal étroit** : Troncature intelligente
- **Logs volumineux** : Pagination automatique
- **Nombreux incidents** : Résumé prioritaire
- **Aucun problème** : Affichage simplifié

---

**Documentation Affichage Détaillé** - Check_depordement.ksh  
**Équipe TechOps** - $(date +%Y-%m-%d)
