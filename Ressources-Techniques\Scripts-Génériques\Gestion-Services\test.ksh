#!/bin/ksh
#===============================================================================
# Script de lancement des commandes eDSN
#===============================================================================
# Description : Script principal pour l'exécution des commandes eDSN
# Auteur      : Équipe TechOps
# Version     : 2.0 - Restructuré et organisé
# Date        : $(date +%Y-%m-%d)
#
# Paramètres :
#   $1 : Commande à lancer (voir liste des commandes ci-dessous)
#   $2 : Période (format YYYYMM)
#   $3 : Type de Lancement (POL/ENT/ETA ou vide pour UDS)
#   $4 : Nom du Pôle/Société/Établissement
#   $5 : Nom du Gestionnaire
#
# Variables d'environnement requises :
#   INIT_PATH, TMP, UXEXE, CONNECT_STRING, SIGACS
#
# Exemples d'utilisation :
#   ./test.ksh ALIM 202312 POL "POLE_PARIS" "gestionnaire1"
#   ./test.ksh CTLWFERR 202312
#   ./test.ksh LSTDECLMENS 202312 ENT "SOCIETE_A"
#
# GROUPES DE COMMANDES DISPONIBLES :
#
# CONTRÔLE ET ERREURS:
#   CTLWFERR     - Contrôle des processus en erreur
#   DELWFERR     - Suppression des processus en erreur
#   REPM2MERR    - Reprise des erreurs M2M
#   REPM2MERR2   - Reprise avancée des erreurs M2M
#   VERIFCOMPTE  - Vérification des comptes déclarants
#
# GESTION DES CAMPAGNES:
#   OUVC         - Ouverture de campagne
#   VERCAMP      - Vérification et recréation de campagne
#   REOUVCAMP    - Réouverture de campagne
#   REINITCAMP   - Réinitialisation de campagne
#
# ALIMENTATION ET GÉNÉRATION:
#   ALIM         - Alimentation DSN brouillon
#   PREGEN       - Prégénération DSN brouillon
#   GENE         - Génération bande DSN brouillon
#   GENEDEF      - Génération bande DSN réelle
#
# EXTRACTION ET VALIDATION:
#   EXTR         - Extraction DSN mensuelle
#   REPEXTR      - Reprise extraction DSN
#   REPEXTR2     - Reprise extraction DSN (version 2)
#   CTLV         - Contrôle et validation DSN
#   REPCTLV      - Reprise contrôle et validation
#
# ENVOIS:
#   ENVP         - Préparation envoi DSN
#   ANNENVP      - Annulation envois DSN
#   REPENVP      - Reprise préparation envoi
#   ENVV         - Validation envois DSN
#   VALIDENVOI   - Validation des envois période courante/précédente
#
# SIGNALEMENTS:
#   SIGSYNCHR0AT - Synchronisation arrêts de travail
#   SIGSYNCHR0FCT- Synchronisation fins de contrat
#   SIGSYNCHR0RA - Synchronisation reprises arrêt travail
#   SIGDOWNATT   - Téléchargement pièces jointes signalements
#
# LISTES ET RAPPORTS:
#   LSTDECLMENS  - Liste déclarations mensuelles
#   LSTDECLCTRL  - Liste déclarations contrôle
#   QRYCTRL      - Rapport déclarations contrôle
#   QRYMENS      - Rapport déclarations mensuelles
#
# PURGE ET MAINTENANCE:
#   PURGECTL     - Purge des DSN de contrôle
#
# PRODUCTION ET CRM:
#   PRODCRM      - Production fichier taux PAS
#   PASM2M       - Génération fichier taux PAS M2M
#   DOWNLOADCRM  - Récupération retours M2M
#   RELOADCRM94  - Rechargement CRM94
#
# INTÉGRATION:
#   INTREPVERS   - Intégration répartition des versements
#
# COLLECTE AER:
#   SCANAERFCTU* - Scan retours AER signalements FCTU
#   COLLECTFCTU* - Collecte retours FCTU par établissement
#   SYNCCRM      - Synchronisation CRM
#===============================================================================

# Chargement des fonctions de base
. $INIT_PATH/shell/function.sh
#===============================================================================
# CONFIGURATION ET INITIALISATION
#===============================================================================

# Récupération des paramètres
Commande=$1
Periode=$2
TypeLance=$3
NomLance=$4
Gestionnaire=$5

# Fonction d'aide
show_usage() {
    echo "Usage: $0 <COMMANDE> <PERIODE> [TYPE_LANCE] [NOM_LANCE] [GESTIONNAIRE]"
    echo ""
    echo "Paramètres:"
    echo "  COMMANDE     : Commande eDSN à exécuter"
    echo "  PERIODE      : Période au format YYYYMM (ex: 202312)"
    echo "  TYPE_LANCE   : Type de lancement (POL/ENT/ETA, optionnel)"
    echo "  NOM_LANCE    : Nom du pôle/société/établissement (optionnel)"
    echo "  GESTIONNAIRE : Nom du gestionnaire (optionnel)"
    echo ""
    echo "Voir l'en-tête du script pour la liste complète des commandes disponibles."
}

# Validation des paramètres obligatoires
if [ -z "$Commande" ]; then
    echo "ERREUR: Paramètre Commande manquant" >&2
    show_usage
    exit 1
fi

if [ -z "$Periode" ]; then
    echo "ERREUR: Paramètre Période manquant" >&2
    show_usage
    exit 1
fi

# Validation du format de période
if ! echo "$Periode" | grep -q '^[0-9]\{6\}$'; then
    echo "ERREUR: Format de période invalide: $Periode (attendu: YYYYMM)" >&2
    exit 1
fi

# Configuration de la version eDSN
GetVarProfile dsnbin
edsnversion="$(echo $PROFVAR | awk -F"/" '{print $(NF - 1)}' | awk -F"-" '{print $2}' | sed "s:\.::g")"
[ "$edsnversion" = "" ] && edsnversion=220

# Définition des options selon la version eDSN
[ $edsnversion -gt 210 ] && newcmd="-"
[ $edsnversion -gt 220 ] && newcmd221="-w -1"
[ $(echo "${edsnversion}" | cut -c1-3) -gt 400 ] && newcmd400="-n Ctlr-$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")"

# Variables de date et temps
export DATDEB=$(date +'%H:%M:%S')
export DEBTRT=$(date +'%s')
export DATJOUR=$(date +'%Y-%m-%d')
export DATCMPGN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

# Configuration du type de lancement
if [ "${TypeLance}" = "" ]; then
    TypeLance2=UDS
    NomLance2="*"
fi

case "${TypeLance}" in
    POL)
        Option="-p"
        ;;
    ENT)
        Option="-s"
        ;;
    ETA)
        Option="-e"
        ;;
    "")
        Option=""
        ;;
    *)
        echo "ERREUR: Type de lancement invalide: $TypeLance (attendu: POL, ENT, ETA ou vide)" >&2
        exit 1
        ;;
esac

# Configuration de la connexion eDSN
EDSN_CONNECT_STRING=${CONNECT_STRING}

if [ "x${APPLI_EDSN_USER}" = "xEDSN" ]; then
    typeset -u L
    typeset -l U
    U=${APPLI_EDSN_USER}
    L=$LOGNAME
    EDSN_CONNECT_STRING=${APPLI_EDSN_USER}/$(psw ${APPLI_EDSN_HOST} $U $L)
fi

export EDSN_CONNECT_STRING

# Affichage des informations de démarrage
echo "==============================================================================="
echo "Démarrage du script eDSN"
echo "==============================================================================="
echo "Commande      : $Commande"
echo "Période       : $Periode"
echo "Type lancement: ${TypeLance:-UDS}"
echo "Nom lancé     : ${NomLance:-Tous}"
echo "Gestionnaire  : ${Gestionnaire:-Non spécifié}"
echo "Version eDSN  : $edsnversion"
echo "Heure début   : $DATDEB"
echo "==============================================================================="
#===============================================================================
# TRAITEMENT PRINCIPAL - DISPATCH DES COMMANDES
#===============================================================================

case "${Commande}" in

#===============================================================================
# GROUPE: CONTRÔLE ET GESTION DES ERREURS
#===============================================================================

CTLWFERR)
    echo ">>> Contrôle des processus en erreur pour la période $Periode"

    # Récupération de la liste des processus en erreur
    LaunchEDSN "dsnworkflow:list-processes -o $TMP/CTLWFERR_list_DSNERR_${Periode}.txt -e ${Periode}"

    # Filtrage des déclarations de contrôle
    grep "Declaration de controle" $TMP/CTLWFERR_list_DSNERR_${Periode}.txt | awk '{print $1}' > $TMP/CTLWFERR_list_DSNERR_${Periode}.txt2
    mv $TMP/CTLWFERR_list_DSNERR_${Periode}.txt2 $TMP/CTLWFERR_list_DSNERR_${Periode}.txt

    if [ -s $TMP/CTLWFERR_list_DSNERR_${Periode}.txt ]; then
        echo "Processus en erreur trouvés: $(wc -l < $TMP/CTLWFERR_list_DSNERR_${Periode}.txt)"

        # Détail de chaque processus en erreur
        cat $TMP/CTLWFERR_list_DSNERR_${Periode}.txt | while read nuproc; do
            echo "Analyse du processus: $nuproc"
            LaunchEDSN "dsnworkflow:detail-process $nuproc"
        done

        # Sauvegarde du détail
        cp ${FICLOG} $TMP/CTLWFERR_detail_process_DSNERR_${Periode}.txt
        echo "Détail sauvegardé dans: $TMP/CTLWFERR_detail_process_DSNERR_${Periode}.txt"
    else
        echo "Aucun processus en erreur trouvé"
    fi
    ;;

DELWFERR)
    echo ">>> Suppression des processus en erreur pour la période $Periode"

    if [ ! -s $TMP/DELWFERR_list_DSNERR_${Periode}.txt ]; then
        echo "ERREUR: Fichier de liste des processus en erreur introuvable"
        echo "Exécutez d'abord CTLWFERR pour générer la liste"
        exit 1
    fi

    echo "Suppression des processus listés dans: $TMP/DELWFERR_list_DSNERR_${Periode}.txt"
    LaunchEDSN "dsntest:cancel${newcmd}byprocessid ${newcmd221} -b -f @$TMP/DELWFERR_list_DSNERR_${Periode}.txt"
    ;;

#===============================================================================
# GROUPE: PURGE ET MAINTENANCE
#===============================================================================

PURGECTL)
    echo ">>> Purge des DSN de contrôle (conservation des $NBMOIS derniers mois)"

    # Configuration du nombre de mois à conserver
    [ -z "${NBMOIS}" ] && NBMOIS=2
    PERIODEDSN_LIMITE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm" "-${NBMOIS}m")

    echo "Période limite de purge: $PERIODEDSN_LIMITE"

    # Récupération des périodes à purger
    echo "SELECT DISTINCT '##'||PERIODEDSN FROM DSNDECLARATION WHERE DSNMODE='TEST' and PERIODEDSN <= '${PERIODEDSN_LIMITE}';\nquit" | sqlplus -s ${EDSN_CONNECT_STRING} \
    | grep "^##" | cut -c3- | sort -n | while read periode_paie; do
        echo "Purge de la DSN de contrôle du mois ${periode_paie}"
        LaunchEDSN "dsntest:cancel -f -m ALL -w 350 ${periode_paie}"
    done
    ;;
#===============================================================================
# GROUPE: GESTION DES SIGNALEMENTS
#===============================================================================

SIGSYNCHR0AT)
    echo ">>> Synchronisation des signalements ARRÊT DE TRAVAIL"

    # Configuration de la date de début (30 jours par défaut)
    [ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
    echo "Date de début: $DATDEBUT"

    # Lancement de la synchronisation
    LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_at.txt ARRET_TRAVAIL"

    if [ -s $TMP/dsnsig_at.txt ]; then
        echo "Processus de synchronisation créés: $(wc -l < $TMP/dsnsig_at.txt)"

        # Attente de la fin de la synchronisation
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_at.txt"

        # Génération des rapports pour chaque processus
        while read numProcess; do
            echo "Génération du rapport pour le processus: $numProcess"

            ficReport=$TMP/ficReport_$$.txt
            ficEtat=$TMP/ETAT_ALIM_SIG_ARRET_TRAVAIL_$(date +%Y%m%d%H%M).csv

            # Nettoyage des fichiers précédents
            rm -f $TMP/ficReport_* "$ficEtat"

            export NUM_PROCESSUS=$numProcess
            LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"

            # Extraction des informations d'erreur
            nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
            listficReport=$(egrep -e "^$numProcess" "$ficReport")
            export nb_erreur=$nb_erreur

            echo "Nombre d'événements en erreur: $nb_erreur"
            echo "Identifiant processus | Utilisateur | Type de signalement   | Total Événements à traiter | Événements traités | Événements en erreur | Événements déjà pris en compte"
            echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
            echo "$listficReport"

            # Téléchargement du rapport détaillé
            rm -f $ficReport
            LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"

            # Vérification de la taille du fichier généré
            if [ "$(wc -c < ${ficEtat})" -gt 221 ]; then
                echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                echo "Rapport généré: $ficEtat"
            else
                echo "${ficEtat} vide, donc non envoyé par mail"
            fi
        done < $TMP/dsnsig_at.txt

        # Notification finale
        if [ $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]; then
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
        else
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        fi
    else
        echo "Aucun processus de synchronisation créé"
    fi
    ;;
SIGSYNCHR0FCT)
    echo ">>> Synchronisation des signalements FIN DE CONTRAT"

    # Configuration de la date de début (30 jours par défaut)
    [ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
    echo "Date de début: $DATDEBUT"

    # Lancement de la synchronisation
    LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_fct.txt FIN_CONTRAT"

    if [ -s $TMP/dsnsig_fct.txt ]; then
        echo "Processus de synchronisation créés: $(wc -l < $TMP/dsnsig_fct.txt)"

        # Attente de la fin de la synchronisation
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_fct.txt"

        # Génération des rapports (même logique que SIGSYNCHR0AT)
        while read numProcess; do
            echo "Génération du rapport pour le processus: $numProcess"

            ficReport=$TMP/ficReport_$$.txt
            ficEtat=$TMP/ETAT_ALIM_SIG_FIN_CONTRAT_$(date +%Y%m%d%H%M).csv

            rm -f $TMP/ficReport_* "$ficEtat"

            export NUM_PROCESSUS=$numProcess
            LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"

            nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
            listficReport=$(egrep -e "^$numProcess" "$ficReport")
            export nb_erreur=$nb_erreur

            echo "Nombre d'événements en erreur: $nb_erreur"
            echo "Identifiant processus | Utilisateur | Type de signalement   | Total Événements à traiter | Événements traités | Événements en erreur | Événements déjà pris en compte"
            echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
            echo "$listficReport"

            rm -f $ficReport
            LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"

            if [ "$(wc -c < ${ficEtat})" -gt 221 ]; then
                echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                echo "Rapport généré: $ficEtat"
            else
                echo "${ficEtat} vide, donc non envoyé par mail"
            fi
        done < $TMP/dsnsig_fct.txt

        # Notification finale
        if [ $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]; then
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
        else
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        fi
    else
        echo "Aucun processus de synchronisation créé"
    fi
    ;;
SIGSYNCHR0RA)
    echo ">>> Synchronisation des signalements REPRISE ARRÊT DE TRAVAIL"

    # Configuration de la date de début (30 jours par défaut)
    [ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
    echo "Date de début: $DATDEBUT"

    # Lancement de la synchronisation
    LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_ra.txt REPRISE_ARRET_TRAVAIL"

    if [ -s $TMP/dsnsig_ra.txt ]; then
        echo "Processus de synchronisation créés: $(wc -l < $TMP/dsnsig_ra.txt)"

        # Attente de la fin de la synchronisation
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_ra.txt"

        # Génération des rapports (même logique que les autres signalements)
        while read numProcess; do
            echo "Génération du rapport pour le processus: $numProcess"

            ficReport=$TMP/ficReport_$$.txt
            ficEtat=$TMP/ETAT_ALIM_SIG_REPRISE_ARRET_TRAVAIL_$(date +%Y%m%d%H%M).csv

            rm -f $TMP/ficReport_* "$ficEtat"

            export NUM_PROCESSUS=$numProcess
            LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"

            nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
            listficReport=$(egrep -e "^$numProcess" "$ficReport")
            export nb_erreur=$nb_erreur

            echo "Nombre d'événements en erreur: $nb_erreur"
            echo "Identifiant processus | Utilisateur | Type de signalement   | Total Événements à traiter | Événements traités | Événements en erreur | Événements déjà pris en compte"
            echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
            echo "$listficReport"

            rm -f $ficReport
            LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"

            if [ "$(wc -c < ${ficEtat})" -gt 221 ]; then
                echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                echo "Rapport généré: $ficEtat"
            else
                echo "${ficEtat} vide, donc non envoyé par mail"
            fi
        done < $TMP/dsnsig_ra.txt

        # Notification finale
        if [ $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]; then
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
        else
            $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        fi
    else
        echo "Aucun processus de synchronisation créé"
    fi
    ;;
# Versions simplifiées des signalements (sans rapport détaillé)
SIGSYNCHR0ATOLD)
    echo ">>> Synchronisation ARRÊT DE TRAVAIL (version simplifiée)"
    LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_at.txt ARRET_TRAVAIL"
    if [ -s $TMP/dsnsig_at.txt ]; then
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_at.txt"
        echo "Synchronisation terminée"
    fi
    ;;

SIGSYNCHR0FCTOLD)
    echo ">>> Synchronisation FIN DE CONTRAT (version simplifiée)"
    LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_fct.txt FIN_CONTRAT"
    if [ -s $TMP/dsnsig_fct.txt ]; then
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_fct.txt"
        echo "Synchronisation terminée"
    fi
    ;;

SIGSYNCHR0RAOLD)
    echo ">>> Synchronisation REPRISE ARRÊT DE TRAVAIL (version simplifiée)"
    LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_ra.txt REPRISE_ARRET_TRAVAIL"
    if [ -s $TMP/dsnsig_ra.txt ]; then
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_ra.txt"
        echo "Synchronisation terminée"
    fi
    ;;

SIGDOWNATT)
    echo ">>> Téléchargement des pièces jointes des signalements"
    PERIODE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm")
    echo "Période: $PERIODE"
    LaunchEDSN "dsnsig:download-attachments ${PERIODE}"
    ;;

#===============================================================================
# GROUPE: VALIDATION DES ENVOIS
#===============================================================================

VALIDENVOI)
    echo ">>> Validation des envois période courante et précédente"

    PERIODE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm")
    echo "Période de référence: $PERIODE"

    # Récupération des périodes à valider
    echo "SELECT DISTINCT '##'||PERIODEDSN FROM DSNDECLARATION WHERE DSNMODE='PROD' AND PERIODEDSN <= '${PERIODE}';\nquit" | sqlplus -s ${EDSN_CONNECT_STRING} | grep "^##" | cut -c3- | sort -r | while read periode_paie; do
        echo "Validation des envois pour la période: $periode_paie"

        LaunchEDSN "dsnsend:list -x -o $TMP/liste_envois_${periode_paie}.txt ${periode_paie} NET"

        if [ -s $TMP/liste_envois_${periode_paie}.txt ]; then
            echo "$(wc -l < $TMP/liste_envois_${periode_paie}.txt) envois à valider"
            LaunchEDSN "dsnsend:validate -f @$TMP/liste_envois_${periode_paie}.txt"
        else
            echo "Aucun envoi à valider pour la période $periode_paie"
        fi
    done
    ;;
#===============================================================================
# GROUPE: ALIMENTATION ET GÉNÉRATION DSN
#===============================================================================

ALIM)
    echo ">>> Alimentation de la bande DSN Brouillon"
    echo "Type de lancement: ${TypeLance:-UDS}, Nom: ${NomLance:-Tous}"

    # Suppression des DSN de contrôle selon la configuration
    case $Slice in
        70)
            echo "Mode Slice 70: Suppression de toutes les DSN de contrôle"
            LaunchEDSN "dsntest:cancel ${newcmd221} -m ALL -f ${Option} '${NomLance}' ${Periode}"
            ;;
        *)
            echo "Mode standard: Suppression des DSN de contrôle non topées comme référence"
            LaunchEDSN "dsntest:cancel ${newcmd221} -m ALL -f -t NOTTESTED ${Option} '${NomLance}' ${Periode}"
            ;;
    esac

    # Création des DSN de contrôle (déclarations)
    echo "Création des DSN de contrôle pour la période $Periode"
    LaunchEDSN "dsntest:create -x -o $TMP/ALIM_list_DSNId_${Periode}.txt -t ${TypeLance}${TypeLance2} ${newcmd400} -n Ctlr-${S_DATRAIT} ${Periode} '${NomLance}'${NomLance2}"

    # Attente de l'extraction
    if [ -s $TMP/ALIM_list_DSNId_${Periode}.txt ]; then
        echo "$(wc -l < $TMP/ALIM_list_DSNId_${Periode}.txt) DSN créées, attente de l'extraction"
        LaunchEDSN "dsntest:wait-extraction${newcmd}byprocessid @$TMP/ALIM_list_DSNId_${Periode}.txt"
    fi
    ;;
PREGEN)
        #---- Prégénération de la bande DSN Brouillon
	rm -f $TMP/crefs.*
        # - Comparaison des DSN de contrôles générées avec les DSN topées comme référence
        LaunchEDSN "dsntest:comparerefs -o $TMP ${Option} '${NomLance}' ${Periode} crefs"
        LaunchEDSN "dsntest:list -t NOTTESTED -o $TMP/PREGEN_dsnCTL_NONREF_${Periode}.txt ${Option} '${NomLance}' ${newcmd400} ${Periode}"
        #recherche les dsn de ctrl identique aux ref à sup
        #     => Si la référence est identique à la DSN de contrôle générée, on supprime la DSN de contrôle générée
        #     => Si la référence est différente de la DSN de contrôle générée, on conserve la DSN de contrôle générée; la DSN de référence associée ne sera pas pris en compte pour générer la bande DSN
        cat $TMP/PREGEN_dsnCTL_NONREF_${Periode}.txt | awk -F"|" '{if(NR>2){print $4$5$6$8$9$10$11$2}}' > $TMP/PREGEN_dsnCTL_NONREF_cle_${Periode}.txt
        cat $TMP/crefs.ok | awk -F"|" '{if(NR>2){print $5$6$7$9$10$11$12}}' > $TMP/crefs.ok_cle.txt
        grep -f $TMP//crefs.ok_cle.txt $TMP/PREGEN_dsnCTL_NONREF_cle_${Periode}.txt | awk '{print $NF}' > $TMP/PREGEN_dsnctlasupp_${Periode}.txt
        cat $TMP/crefs.ko | awk -F"|" '{if(NR>2){print $2}}' | awk '{print $NF}' > $TMP/PREGEN_dsnrefasupp_${Periode}.txt
	#---- Modif 2016/01/09 : Problème sur le script de génération des DSN de contrôle : les espaces ne sont pas identiques
	#if [ -s $TMP/PREGEN_dsnctlasupp_${Periode}.txt ]
	if [ -s $TMP/crefs.clean ]
	then
		cat $TMP/crefs.clean | awk -F"|" '{if(NR>2){print $2}}' | sed "s/ *//g" > $TMP/PREGEN_dsnctlasupp_${Periode}.txt
        	LaunchEDSN "dsntest:cancel${newcmd}byprocessid ${newcmd221} -f @$TMP/PREGEN_dsnctlasupp_${Periode}.txt"
	fi
        ;;
GENE)
        #---- Génération de la bande DSN Brouillon
        #On génère une bande DSN avec :
        # - Les déclarations de référence identiques aux DSN de contrôle (qui ont été supprimées)
        # - Les déclarations générées différentes des déclarations de référence
        # - Les déclarations générées sans déclaration de référence
	[ -z ${PFDSN} ] && PFDSN=pfdsn
        #LaunchEDSN "dsntest:list ${Option} '${NomLance}' -x -o $TMP/GENE_mesDSN_${NomLance}.txt ${newcmd400} ${Periode}"
        LaunchEDSN "dsntest:list ${Option} '${NomLance}' -x -o $TMP/GENE_mesDSN_${NomLance}.txt ${Periode}"
        grep -vf $TMP/PREGEN_dsnrefasupp_${Periode}.txt $TMP/GENE_mesDSN_${NomLance}.txt > $TMP/GENE_mesDSN_${NomLance}_${Periode}.txt
        LaunchEDSN "dsntest:gen-file -f ${PFDSN} @$TMP/GENE_mesDSN_${NomLance}_${Periode}.txt"
        ;;
GENEDEF)
        #---- Génération de la bande DSN Reel en remplacement de PMDSN4 avec M2M
        [ -z ${PFDSN} ] && PFDSN=pfdsn
        LaunchEDSN "dsn:list-declarations ${Option} '${NomLance}' -m PROD -x -o $TMP/GENEDEF_mesDSN_${NomLance}_${Periode}.txt ${Periode}"
        LaunchEDSN "dsn:gen-file -s ${PFDSN} @$TMP/GENEDEF_mesDSN_${NomLance}_${Periode}.txt"
        ;;
REINITCAMP)
	#---- Réinitialisation de la campagne pour un établissement
	#On extrait les déclarations
	CDETAB=$4
	LaunchEDSN "dsnworkflow:list-processes --etab ${CDETAB} -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
        LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt -t TBE ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:extract @$TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt"
                LaunchEDSN "dsn:wait-extraction -f -e '${NomLance}' ${Periode}"
        fi
	#On valide les TBV
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt -t TBV ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt"
        fi
	#On valide les TBC
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt -t TBC ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt"
		sleep 5
        fi
	#On supprimes les déclarations
	LaunchEDSN "dsn:delete-declarations ${Option} '${NomLance}' -f ${Periode}"
	;;
OUVC)
        #---- Ouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/OUVC_mesCampagne_${Periode}.txt ${Option} '${NomLance}' ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/OUVC_mesCampagne_${Periode}.txt"
        ;;
VERCAMP)
	#---- Vérification et recréation de la campagne
	LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/VERCAMP_mesUD_${NomLance}_${Periode}.txt ${Periode}"
	LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -t TBE -o $TMP/VERCAMP_mesTask_${NomLance}_${Periode}.txt ${Periode}"
	REOUV=0
	#---- Boucle sur les établissements du pôle
	while read ligne
	do
		letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
		if [ $(grep -c "${letab}" $TMP/VERCAMP_mesTask_${NomLance}_${Periode}.txt) -eq 0 ]
		then
			REOUV=1
			#Extraire la déclaration de l'établissement
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt -e ${letab} -t TBE ${Periode}"
        		if [ -s $TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:extract @$TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt"
                		LaunchEDSN "dsn:wait-extraction -e ${letab} ${Periode}"
        		fi
			#Valider les TBV de l'établissement concerné
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt -e ${letab} -t TBV ${Periode}"
        		if [ -s $TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:validate @$TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt"
        		fi
			#Valider les TBC de l'établissement concerné
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt -e ${letab} -t TBC ${Periode}"
        		if [ -s $TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt"
        		fi
			#Supprimer les déclarations de l'établissement concerné
			LaunchEDSN "dsn:delete-declarations -e ${letab} -f ${Periode}"
		fi
	done < $TMP/VERCAMP_mesUD_${NomLance}_${Periode}.txt
	if [ "$REOUV" = "1" ]
	then
		#réouvrir la campagne pour le pole concerné
		LaunchEDSN "campaign:list -t NOTOPEN ${Option} '${NomLance}' -x -o $TMP/VERCAMP_mesCampagne_${Periode}_${NomLance}.txt ${Periode}"
		LaunchEDSN "campaign:open ${Periode} @$TMP/VERCAMP_mesCampagne_${Periode}_${NomLance}.txt"
	fi
	;;
REOUVCAMP)
	#---- Réitialisation des campagnes
	#On reset les campagnes
        LaunchEDSN "dsnworkflow:list-processes -o $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313_a.txt ${Periode}"
        grep mensuelle $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313_a.txt|grep "Demarrage de l'alimentation"|cut -d" " -f1 > $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt
        if [ -s $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt ]
        then
                while read nuprocareinit
                do
                        echo "dsnworkflow:reset-process $nuprocareinit">$TMP/in_reset_${nuprocareinit}.txt
                        echo "oui" >>$TMP/in_reset_${nuprocareinit}.txt
                        echo "logout" >>$TMP/in_reset_${nuprocareinit}.txt
                        ./client <$TMP/in_reset_${nuprocareinit}.txt
                        rm $TMP/in_reset_${nuprocareinit}.txt
                done < $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt
        fi
	LaunchEDSN "dsn:list-tasks -t TBE ${Periode}"
        #---- Réitialisation de la campagne 2.2.4 vers 3.1.3
        #On extrait les déclarations
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt -t TBE ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:extract @$TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt"
                LaunchEDSN "dsn:wait-extraction -f ${Periode}"
        fi
        #On valide les TBV
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt -t TBV ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt"
        fi
        #On valide les TBC
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt -t TBC ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors -w -1 @$TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt"
        fi
        #On supprimes les déclarations
        LaunchEDSN "dsn:delete-declarations -f ${Periode}"
	if [ "$REOUVCAMP" = "1" ]
	then
        	#---- Ouverture de la campagne
        	LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REOUVCAMP_mesCampagne_${Periode}_224_vers_313.txt -t NOTOPEN ${Periode}"
		LaunchEDSN "campaign:open ${Periode} @$TMP/REOUVCAMP_mesCampagne_${Periode}_224_vers_313.txt "
	fi
	;;
EXTR)
        #---- Extraction de la bande DSN Mensuelle
        # - Extraction de toutes les déclarations Mensuelle
        LaunchEDSN "dsn:list-tasks -x -o $TMP/EXTR_mesDSNTBE_${Periode}.txt -t TBE ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/EXTR_mesDSNTBE_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:extract @$TMP/EXTR_mesDSNTBE_${Periode}.txt"
        	LaunchEDSN "dsn:wait-extraction ${Option} '${NomLance}' ${Periode}"
		sleep 300
	fi
        # - Validation automatique des déclarations mensuelles identiques aux DSn de contrôle topées en "référence"
        LaunchEDSN "dsn:list-tasks -r REFOK -t TBV -x -o $TMP/EXTR_mesDSNPREVAL_${Periode}.txt ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/EXTR_mesDSNPREVAL_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate @$TMP/EXTR_mesDSNPREVAL_${Periode}.txt"
	fi
        ;;

REPEXTR2)
        #---- Reprise de l'Extraction de la bande DSN Mensuelle
        #---- Validation des déclarations à l'état TBV
        LaunchEDSN "dsnworkflow:list-processes -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
        LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done
;;
REPEXTR)
        #---- Reprise de l'Extraction de la bande DSN Mensuelle
        #---- Validation des déclarations à l'état TBV
	LaunchEDSN "dsnworkflow:list-processes -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
	LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done

        LaunchEDSN "dsn:list-tasks -x -o $TMP/REPEXTR_mesDSNVAL_${Periode}.txt -t TBV ${Periode}"
        if [ -s $TMP/REPEXTR_mesDSNVAL_${Periode}.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REPEXTR_mesDSNVAL_${Periode}.txt"
        fi
        #---- Forçage de validation des déclarations à l'état TBC
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt -t TBC ${Periode}"
        if [ -s $TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt"
        fi
        #---- Supprimer les déclarations
	LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPEXTR_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPEXTR_mesCampagne_${Periode}.txt"
        ;;
CTLV)
        #---- Controle et Validation de la bande DSN Mensuelle
        # Cette étape valide automatiquement toutes les DSN mensuelles non validée (à valider + à corriger)
        # Obligatoire afin d'avoir toutes les déclarations dans les bandes mensuelles (EDSN n.extrait les déclarations dans une bande que si elles sont à l.état valide)
        LaunchEDSN "dsn:list-tasks -x -o $TMP/CTLV_mesDSNVal_${Periode}.txt -t TBV ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/CTLV_mesDSNVal_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate @$TMP/CTLV_mesDSNVal_${Periode}.txt"
	fi
        LaunchEDSN "dsn:list-tasks -x -o $TMP/CTLV_mesDSNValWithErr_${Periode}.txt -t TBC ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/CTLV_mesDSNValWithErr_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/CTLV_mesDSNValWithErr_${Periode}.txt"
	fi
        ;;
REPCTLV)
        #---- Reprise du Controle et Validation de la bande DSN Mensuelle
        #---- Supprimer les déclarations
	LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPCTLV_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPCTLV_mesCampagne_${Periode}.txt"
	;;
ENVP)
        #---- Préparation et constitution de l'envoi de la bande DSN Mensuelle (selon découpage)
        # Cette étape génère la bande DSN Mensuelle
	#sleep 120
        LaunchEDSN "dsnsend:list-pendingdeclarations ${Option} '${NomLance}' -x -o $TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt ${Periode}"
        #if [ $edsnversion -gt 350 ]
        if [ $(echo "${edsnversion}" | cut -c1-3) -gt 350 ]
        then
		LaunchEDSN "dsnsend:prepare -g superviseurPole(${NomLance}) -x -o $TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt @$TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt"
        else
		LaunchEDSN "dsnsend:prepare -u ${Gestionnaire} -x -o $TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt @$TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt"
        fi
        LaunchEDSN "dsnsend:wait-preparation @$TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt"
        mkdir -p $TMP/${NomLance}_${Periode}
        LaunchEDSN "dsnsend:do-send $TMP/${NomLance}_${Periode} @$TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt"
        #Copie des fichiers produits pour la sauvegarde légale
        mkdir -p $SIGACS/SauvegardeLegale/DSN/
        cp $TMP/${NomLance}_${Periode}/* $SIGACS/SauvegardeLegale/DSN/
        ;;
ANNENVP)
	#---- Annulation des Envois de DSN Mensuelle
        #---- Liste des Envois NET
        LaunchEDSN "dsnsend:list -x -o $TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt ${Periode} NET"
        #---- Annuler les Envois NET
        if [ -s $TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt"
        fi
        #---- Liste des Envois MSA
        LaunchEDSN "dsnsend:list -x -o $TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt ${Periode} MSA"
        if [ -s $TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt "
        fi
        ;;
REPENVP)
        #---- Reprise de la Préparation et constitution de l'envoi de la bande DSN Mensuelle (selon découpage)
        #---- Liste des Envois NET
        LaunchEDSN "dsnsend:list -x -o $TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt ${Periode} NET"
        #---- Annuler les Envois NET
        if [ -s $TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt"
        fi
        #---- Liste des Envois MSA
        LaunchEDSN "dsnsend:list -x -o $TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt ${Periode} MSA"
        if [ -s $TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt "
        fi
        #---- Supprimer les déclarations sinon rien ne sera à réouvrir
        LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPENVP_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPENVP_mesCampagne_${Periode}.txt"
	;;
ENVV)
        #---- Passage des DSN Mensuelle en attente de conformité
        LaunchEDSN "dsnsend:validate -f @$TMP/ENVP_mesEnvois_${Periode}.txt"
        ;;
LSTDECLMENS)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations -x -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations -x -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLMENS_CRM)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations -x -o $TMP/TOTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
                LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}.txt"
        else
                LaunchEDSN "dsn:list-declarations -x -o $TMP/TOTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
                LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}_${NomLance}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt"
        fi
	LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}.txt"
        ;;
LSTDECLPAS)
        #---- Liste des déclarations de type Mensuel pour CRM PAS DGFIP
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations --last-version-only -x -o $TMP/LSTDECLPAS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations --last-version-only -x -o $TMP/LSTDECLPAS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLMENSX)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations  -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations  -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLCTRL)
        #---- Liste des déclarations de type Brouillon
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsntest:list -x -o $TMP/LSTDECLCTRL_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsntest:list -x -o $TMP/LSTDECLCTRL_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
QRYCTRL)
        #---- Exécution d'1 rapport pour les déclarations de type Brouillon
        cmdFORMAT=
        case "$NOMFIC" in
        	*.xlsx)
        		cmdFORMAT="-f XLSX"
        		;;
        	*.xml)
        		cmdFORMAT="-f XML"
        		;;
        esac
        LaunchEDSN "dsntest:dsnreport $cmdFORMAT -o $TMP/${NOMFIC} ${RAPPORT} @${FICDECL}"
	;;
QRYMENS)
        #---- Exécution d'1 rapport pour les déclarations de type Mensuel
        cmdFORMAT=
        case "$NOMFIC" in
        	*.xlsx)
        		cmdFORMAT="-f XLSX"
        		;;
        	*.xml)
        		cmdFORMAT="-f XML"
        		;;
        esac
        LaunchEDSN "dsn:dsnreport $cmdFORMAT -o $TMP/${NOMFIC} ${RAPPORT} @${FICDECL}"
        ;;
QRYMENS331)
        #---- Exécution d'1 rapport pour les déclarations de type Mensuel (Uniquement pour palier au bug de la Version 3.3.1)
        while read numdec
        do
                LaunchEDSN "dsn:dsnreport -o $TMP/ANO_CORR_${RAPPORT}_${numdec}_$$_${Periode}.csv ${RAPPORT} ${numdec}"
        done < ${FICDECL}
        #---- Concaténation
        pfichier=""
        ls -1 $TMP/ANO_CORR_${RAPPORT}_*_$$_${Periode}.csv|while read numfic
        do
                if [ "$pfichier" = "" ]
                then
                        pfichier="X"
                        cat $numfic > $TMP/${NOMFIC}
                else
                        awk '{if (NR>1){print $0;}}' $numfic >> $TMP/${NOMFIC}
                fi
        done
        rm $TMP/ANO_CORR_${RAPPORT}_*
        ;;
LSTDECLSIG)
        #---- Liste des déclarations de type Signalement
                        LaunchEDSN "dsnsig:list-declarations -x -o $TMP/LSTDECLSIG_mesSignalements_${RAPPORT}_${Periode}.txt ${Periode} ${RAPPORT}"
            ;;
QRYSIG)
        #---- Exécution d'1 rapport pour les signalements [SIGNALEMENT_FIN_CONTRAT, SIGNALEMENT_ARRET_TRAVAIL, SIGNALEMENT_REPRISE_ARRET_TRAVAIL]
        LaunchEDSN "dsnsig:dsnreport -o $TMP/${NOMFIC} signalement @${FICDECL}"
        ;;
PRODCRM)
	#---- Generation fichier taux du pas a partir des retours dgfip fournis par le client
        if [ $(echo "${edsnversion}" | cut -c1) -lt 5 ]
        then
        LaunchEDSN "admin:produce-crm ${REPCIBLE}"
        else
        LaunchEDSN "pas:extract-from-net-e-files -f XML -fn ${NOMFIC} ${REPCIBLE} ${DATE} ${OUTCIBLE}"
        grep "KO" $FILE/traitements.txt | cut -c1-36 > $FILE/traitementsKO.txt
        rm $FILE/detail-traitements-KO.txt
         if [ -s $FILE/traitementsKO.txt ]
         then
        #boucle sur ces identifiants KO
                 while read ident
                 do
                 LaunchEDSN "DSN:DETAIL-DECLARATION $ident"
                 done < $FILE/traitementsKO.txt
          cp ${FICLOG} $FILE/detail-traitements-KO.txt
          echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$FILE/detail-traitements-KO.txt";OK" >> $TMP/CR_${S_CODSESS}.lst
         fi
        fi
	;;
PASM2M)
        # generation fichier taux du Pas
        LaunchEDSN "pas:extract-from-m2m-decl -f XML -fn ${NOMFIC} ${OUTCIBLE} @${FICDECL}"
        rm $FILE/detail-traitements-KO.txt
        grep "KO" $FILE/traitements.txt | cut -c1-36 > $FILE/traitementsKO.txt
        if [ -s $FILE/traitementsKO.txt ]
         then
        #boucle sur ces identifiants KO
                 while read ident
                 do
                 LaunchEDSN "DSN:DETAIL-DECLARATION $ident"
                 done < $FILE/traitementsKO.txt
          cp ${FICLOG} $FILE/detail-traitements-KO.txt
          echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$FILE/detail-traitements-KO.txt";OK" >> $TMP/CR_${S_CODSESS}.lst
         fi
        ;;
REPM2MERR)
        LaunchEDSN "dsnworkflow:list-m2m-send-error-processes -x -o $TMP/CTL_list_M2MERR_${Periode}.txt ${Periode}"
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
        LaunchEDSN "dsnworkflow:retry-erroneous-processes @$TMP/CTL_list_M2MERR_${Periode}.txt"
        fi
        ;;
REPM2MERR2)
        #reprise des depots mensuels et signalement a faire
        RCGPO=0
        for typeEnvoi in m2m m2mSignalement
        do
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $TMP/CTL_list_M2MERR_${Periode}.txt -t $typeEnvoi" </dev/null
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
                LaunchEDSN "dsnworkflow:retry-erroneous-processes @$TMP/CTL_list_M2MERR_${Periode}.txt" </dev/null
                # Liste des depots M2M
                LaunchEDSN "async:list-jobs -x -o $TMP/CTL_list_job1_${Periode}.txt -t $typeEnvoi" </dev/null
                if [ -s $TMP/CTL_list_job1_${Periode}.txt ]
                then
                n1=$(wc -l $TMP/CTL_list_job1_${Periode}.txt)
                echo "Il y a $n1 reprise de depot $typeEnvoi a traiter."
                LaunchEDSN "async:wait-jobs @$TMP/CTL_list_job1_${Periode}.txt" </dev/null
                rm $TMP/CTL_list_job1_${Periode}.txt
                echo "Reprises $typeEnvoi effectuees"
                fi
        else
                echo "Aucune reprise de depot $typeEnvoi a faire"
        fi

         #post controle des reprises non faites
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $TMP/CTL_list_M2MERR_${Periode}.txt -t $typeEnvoi" </dev/null
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
                echo "Il reste des reprises $typeEnvoi a realiser."
                LaunchEDSN "dsnworkflow:list-processes -e -t $typeEnvoi" </dev/null
            RCGPO=`echo $(($RCGPO + $RC + 1 ))`
        fi
        done

        if [ $RCGPO -gt 0 ]
        then
                $SRVNET_DIR/U_POST_UPROC_${Slice} "KO reprise en échec ou incomplète" "$DATDEB" $DEBTRT "$Description"
        fi
;;
VERIFCOMPTE)
        LaunchEDSN "m2m:list-declarants -o $TMP/listedecl.txt"
        while read line
        do
                actif=$(echo $(echo "$line" | awk -F '|' '{print $2}'))
                habilitation=$(echo $(echo "$line" | awk -F '|' '{print $6}'))
                IdCompteDeclarant=$(echo $(echo "$line" | awk -F '|' '{print $1}'))
        echo "actif=$actif"
        echo "IdCompteDeclarant=$IdCompteDeclarant"
        echo "habilitation=$habilitation"
                if [ $actif = "x" ]
                then
                        if [ $habilitation = "NET / MSA" ]
                        then
                        #gestion particuliere point depot NET / MSA : on doit donc tester les 2
                                #test compte point depot NET
                                LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' NET"
                                #test compte point depot MSA
                                LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' MSA"
                        else
                        #test compte pour point depot correspondant
                        LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' $habilitation"
                        fi
                fi
        done < $TMP/listedecl.txt
;;
DOWNLOADCRM)
		#---- récupération des retours M2M
		if [ -z ${LISTRETOUR} ]
   		then
        LaunchEDSN "DSN:DOWNLOAD-ATTACHMENTS -p ${CDPOLE} -x ${PREFIXNMFIC} ${PERIODE}"
		else
        LaunchEDSN "DSN:DOWNLOAD-ATTACHMENTS -p ${CDPOLE} -x ${PREFIXNMFIC} -n ${LISTRETOUR} ${PERIODE}"
        fi
	;;
RELOADCRM94)
		#rechargement CRM94 suite passage DSN V5
	LaunchEDSN "dsn:list-declarations -x -o $TMP/liste_CRM94_${Periode}.txt ${Periode}"
	 if [ -s $TMP/liste_CRM94_${Periode}.txt ]
	 then
		LaunchEDSN "dsn:delete-attachments @$TMP/liste_CRM94_${Periode}.txt"
        	cat $TMP/liste_CRM94_${Periode}.txt|while read nuproc
		do
		LaunchEDSN "m2m:get-gipdocuments-for-declaration $nuproc"
                LaunchEDSN "async:list-jobs -x -o $TMP/Jobs1.txt -t m2mAttachmentDgfipIntegration"
                if [ -s $TMP/Jobs1.txt ]
                then
                LaunchEDSN "async:wait-jobs @$TMP/Jobs1.txt"
		rm $TMP/Jobs1.txt
		fi

		LaunchEDSN "async:list-jobs -x -o $TMP/Jobs2.txt -t m2mAttachmentIntegration"
                if [ -s $TMP/Jobs2.txt ]
		then
                LaunchEDSN "async:wait-jobs @$TMP/Jobs2.txt"
		rm $TMP/Jobs2.txt
		fi
                done
	 else
	 echo "aucun CRM 94 listé en BDSN pour ${Periode}"
	 fi
	 ;;
INTREPVERS)
        # integration repartition des versements
        #
                GetVarProfile dsnconf
                DSNCONF=${PROFVAR}
        # Liste les declarations DSN stockees en BDSN pour la periode
        LaunchEDSN "dsn:list-declarations -x -o $TMP/liste_DSN_${Periode}.txt ${Periode}"
        if [ -s $TMP/liste_DSN_${Periode}.txt ]
        then

                if [ ! -f ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg ] || ! grep -q "^paiement.organismes=" ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg
                then
                export listorg="DGFIP"
                else
                listorg=$(grep "^paiement.organismes=" ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg | cut -d '=' -f2 | sed "s:,:\n:g")
                export listorg
                fi
                echo "listorg=$listorg"

                echo "$listorg" | while read nmorg
                do
                echo $nmorg
                        # Filtre les declarations candidates a la repartition des versements pour DGFIP
                        LaunchEDSN "dsn:list-repartition-candidates -x -o $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt ${Periode} ${nmorg} @$TMP/liste_DSN_${Periode}.txt"
                        if [ -s $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt ]
                        then


                        # lancement de la repartition des versements pour l'organisme DGFIP
                        LaunchEDSN "dsn:start-repartition-versements -x -o $TMP/liste_wait-repartition_${Periode}.txt ${Periode} ${nmorg} @$TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt"
			LaunchEDSN "dsn:wait-repartition-versements @$TMP/liste_wait-repartition_${Periode}.txt"
                        while read iddecl
                        do
                        LaunchEDSN "dsn:status-repartition-versements ${iddecl}"
                        done < $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt
                        else
                        echo "rien a traiter pour ${nmorg}."
                        fi
                done
        fi
        ;;
SCANAERFCTUALL)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
SCANAERFCTU)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
SCANAER2FCTU)
                #---- Recuperation des retours KO AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_KO_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour en erreur AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
COLLECTFCTUOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par etablissement
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                while read ligne
                do
                letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
                case "${Slice}" in
                "AL")
                        export FICZIP=$TMP/AERFCTU_${letab}_${DATEFIN}.zip
                ;;
                *)
                        export FICZIP=$TMP/SIGNFCTU_${letab}_RAER_${DATEDEBUT}.zip
                ;;
		esac
                echo "======================================================================="
                echo "****** debut traitement etablissement ${letab} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -e ${letab} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${letab} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${letab} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTUD_${NomLance}_${Periode}.txt
        #       $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        ;;
COLLECTFCTUKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par etablissement
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                while read ligne
                do
                letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
                export FICZIP=$TMP/SIGNFCTU_KO_${letab}_RAER_${DATEDEBUT}.zip

                echo "======================================================================="
                echo "****** debut traitement etablissement ${letab} `date +'%H:%M:%S'` ******"
                echo "======================================================================="
                LaunchEDSN "m2m:scan-returns -e ${letab} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                                                then
                        echo "${letab} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${letab} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTUD_${NomLance}_${Periode}.txt
#               $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        ;;
SCANAERFCTUOLD)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")
                echo "Slice=$Slice"
                echo "PAYS=$PAYS"
                echo "CODEUG=${S_CODUG}"
                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip
                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm $FICZIP
                fi
        ;;
SCANAER2FCTUOLD)
                #---- Recuperation des retours KO AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")
                export FICZIP=$TMP/SIGNFCTU_KO_RAER_${DATEDEBUT}.zip
                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour en erreur AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm $FICZIP
                fi
        ;;
COLLECTFCTUSOCOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
		while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/AERFCTU_${ste}_${DATEFIN}.zip
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -ste ${ste} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${ste} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $ste"
		else
                        echo "${ste} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
;;
COLLECTFCTUSOCKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
                while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/AERFCTUKO_${ste}_${DATEFIN}.zip
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -ste ${ste} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${ste} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $ste"
                else
                        echo "${ste} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
;;
COLLECTFCTUCLEOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")


                if [ -s $TMP/LISTCLE_${NomLance}_${Periode}.txt ]; then
			while read ligne
			do
				CLE="$(echo "$ligne")"
				export FICZIP=$TMP/AERFCTU_${CLE}_${DATEFIN}.zip
				echo "======================================================================="
				echo "****** debut traitement etablissement ${CLE} `date +'%H:%M:%S'` ******"
				echo "======================================================================="

				LaunchEDSN "m2m:scan-returns -afas1 ${CLE} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
				if [ "$(wc -c < ${FICZIP})" -gt 311 ]
				then
					echo "${CLE} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
					$SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $CLE"
				else
					echo "${CLE} : le fichier ${FICZIP} est vide donc supprimÃ©"
					rm ${FICZIP}
				fi
			done < $TMP/LISTCLE_${NomLance}_${Periode}.txt
		else
			echo "Liste Cle d'affectation non extraite"
			$SRVNET_DIR/U_POST_UPROC_${Slice} "KO" "$DATDEB" $DEBTRT "$Description"
		fi
;;
COLLECTFCTUCLEKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")


                if [ -s $TMP/LISTCLE_${NomLance}_${Periode}.txt ]; then
                        while read ligne
                        do
                                CLE="$(echo "$ligne")"
                                export FICZIP=$TMP/AERFCTUKO_${CLE}_${DATEFIN}.zip
                                echo "======================================================================="
                                echo "****** debut traitement etablissement ${CLE} `date +'%H:%M:%S'` ******"
                                echo "======================================================================="

                                LaunchEDSN "m2m:scan-returns -afas1 ${CLE} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                                then
                                        echo "${CLE} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©" dÃ©p
                                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $CLE"
                                else
                                        echo "${CLE} : le fichier ${FICZIP} est vide donc supprimÃ©"u
                                        rm ${FICZIP}
                                fi
                        done < $TMP/LISTCLE_${NomLance}_${Periode}.txt
                else
                        echo "Liste Cle d'affectation non extraite"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "KO" "$DATDEB" $DEBTRT "$Description"
                fi
;;
SYNCCRM)
                Periode=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm")
                DATEDEBUT=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm" "-1m")
                DATEFIN=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm" "-1m" )
                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
                while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/ATT_${ste}_${Periode}.csv
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="
                LaunchEDSN "atmp:sync-referential-from-m2m --output sync.log --verbose --output-ref /pr121/txt/tmp/ATT_${ste}_${Periode}.csv --ignore-current-ref --type-perimeter ENT ${DATEDEBUT} ${DATEFIN} ${ste}"
                if [ $? -eq 0 ]
                then
                        echo "${ste} : le fichier /pr121/txt/tmp/ATT_${ste}_${Periode}.csv a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${ste} : le fichier /pr121/txt/tmp/ATT_${ste}_${Periode}.csv est vide donc supprimÃ©"

                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
    ;;

#===============================================================================
# COMMANDE INCONNUE
#===============================================================================

*)
    echo "ERREUR: Commande inconnue: $Commande" >&2
    echo ""
    echo "Commandes disponibles par groupe:"
    echo ""
    echo "CONTRÔLE ET ERREURS:"
    echo "  CTLWFERR, DELWFERR, REPM2MERR, REPM2MERR2, VERIFCOMPTE"
    echo ""
    echo "GESTION DES CAMPAGNES:"
    echo "  OUVC, VERCAMP, REOUVCAMP, REINITCAMP"
    echo ""
    echo "ALIMENTATION ET GÉNÉRATION:"
    echo "  ALIM, PREGEN, GENE, GENEDEF"
    echo ""
    echo "EXTRACTION ET VALIDATION:"
    echo "  EXTR, REPEXTR, REPEXTR2, CTLV, REPCTLV"
    echo ""
    echo "ENVOIS:"
    echo "  ENVP, ANNENVP, REPENVP, ENVV, VALIDENVOI"
    echo ""
    echo "SIGNALEMENTS:"
    echo "  SIGSYNCHR0AT, SIGSYNCHR0FCT, SIGSYNCHR0RA, SIGDOWNATT"
    echo "  SIGSYNCHR0ATOLD, SIGSYNCHR0FCTOLD, SIGSYNCHR0RAOLD"
    echo ""
    echo "LISTES ET RAPPORTS:"
    echo "  LSTDECLMENS, LSTDECLCTRL, QRYCTRL, QRYMENS, etc."
    echo ""
    echo "PURGE ET MAINTENANCE:"
    echo "  PURGECTL"
    echo ""
    echo "PRODUCTION ET CRM:"
    echo "  PRODCRM, PASM2M, DOWNLOADCRM, RELOADCRM94"
    echo ""
    echo "INTÉGRATION:"
    echo "  INTREPVERS"
    echo ""
    echo "COLLECTE AER:"
    echo "  SCANAERFCTU*, COLLECTFCTU*, SYNCCRM"
    echo ""
    echo "Consultez la documentation complète: docs/COMMANDS.md"
    echo "Ou utilisez: $0 --help"
    exit 1
    ;;

esac

#===============================================================================
# FIN DU TRAITEMENT
#===============================================================================

# Calcul du temps d'exécution
DATFIN=$(date +'%H:%M:%S')
FINTRT=$(date +'%s')
DUREE=$((FINTRT - DEBTRT))

echo "==============================================================================="
echo "Fin du traitement eDSN"
echo "==============================================================================="
echo "Commande      : $Commande"
echo "Période       : $Periode"
echo "Heure début   : $DATDEB"
echo "Heure fin     : $DATFIN"
echo "Durée         : ${DUREE}s"
echo "==============================================================================="

# Nettoyage optionnel des fichiers temporaires (si variable définie)
if [ "$CLEANUP_TEMP" = "1" ]; then
    echo "Nettoyage des fichiers temporaires..."
    rm -f $TMP/*_${Periode}.txt $TMP/*_$$.txt 2>/dev/null
fi

exit $?
