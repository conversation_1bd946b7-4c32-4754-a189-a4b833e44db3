# Script de lancement des commandes eDSN
# $1 : Commande à lancer
# $2 : Période
# $3 : Type de Lancement (par Pôle ou par Société)
# $4 : Nom du Pôle ou Société
# $5 : Nom du Gestionnaire
. $INIT_PATH/shell/function.sh
Commande=$1
Periode=$2
TypeLance=$3
NomLance=$4
Gestionnaire=$5
#---- on récupère la version de eDSN
GetVarProfile dsnbin
edsnversion="$(echo $PROFVAR | awk -F"/" '{print $(NF - 1)}' | awk -F"-" '{print $2}' | sed "s:\.::g")"
[ "$edsnversion" = "" ] && edsnversion=220
[ $edsnversion -gt 210 ] && newcmd="-"
[ $edsnversion -gt 220 ] && newcmd221="-w -1"
[ $(echo "${edsnversion}" | cut -c1-3) -gt 400 ] && newcmd400="-n Ctlr-$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")"


export DATDEB=`date +'%H:%M:%S'`
export DEBTRT=`date +'%s'`
export DATJOUR=`date +'%Y-%m-%d'`
export DATCMPGN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")


if [ "${TypeLance}" = "" ]
then
	TypeLance2=UDS
	NomLance2="*"
fi
case "${TypeLance}" in
POL)
        Option="-p"
        ;;
ENT)
        Option="-s"
        ;;
ETA)
        Option="-e"
        ;;
esac
#
# VERSIONS CASA/CACIB/LCL
#
EDSN_CONNECT_STRING=${CONNECT_STRING}
#
if [ "x${APPLI_EDSN_USER}" = "xEDSN" ]
then
typeset -u L
typeset -l U
U=${APPLI_EDSN_USER}
L=$LOGNAME
EDSN_CONNECT_STRING=${APPLI_EDSN_USER}/$(psw ${APPLI_EDSN_HOST} $U $L)
fi
export EDSN_CONNECT_STRING
#
case "${Commande}" in
CTLWFERR)
	#---- Controle des processus en erreur
	LaunchEDSN "dsnworkflow:list-processes -o $TMP/CTLWFERR_list_DSNERR_${Periode}.txt -e ${Periode}"
	grep "Declaration de controle" $TMP/CTLWFERR_list_DSNERR_${Periode}.txt | awk '{print $1}' > $TMP/CTLWFERR_list_DSNERR_${Periode}.txt2
	mv $TMP/CTLWFERR_list_DSNERR_${Periode}.txt2 $TMP/CTLWFERR_list_DSNERR_${Periode}.txt
	if [ -s $TMP/CTLWFERR_list_DSNERR_${Periode}.txt ]
	then
		cat $TMP/CTLWFERR_list_DSNERR_${Periode}.txt | while read nuproc
		do
			LaunchEDSN "dsnworkflow:detail-process $nuproc"
		done
		cp ${FICLOG} $TMP/CTLWFERR_detail_process_DSNERR_${Periode}.txt
	fi
	;;
PURGECTL)
        #--- Purge des DSN de contrôle sauf les $NBMOIS derniers mois
        [ -z "${NBMOIS}" ] && NBMOIS=2
	PERIODEDSN_LIMITE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm" "-${NBMOIS}m")
        echo "SELECT DISTINCT '##'||PERIODEDSN FROM DSNDECLARATION WHERE DSNMODE='TEST' and PERIODEDSN <= '${PERIODEDSN_LIMITE}';\nquit" | sqlplus -s ${EDSN_CONNECT_STRING} \
        | grep "^##" | cut -c3- | sort -n | while read periode_paie
        do
          echo "Purge de la DSN de contrôle du mois ${periode_paie}"
          LaunchEDSN "dsntest:cancel -f -m ALL -w 350 ${periode_paie}"
        done
        ;;
SIGSYNCHR0AT)
        # synchronisation AT
[ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
        LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_at.txt ARRET_TRAVAIL"
        if [ -s $TMP/dsnsig_at.txt ]
        then
         LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_at.txt"
        #generation rapport
                while read numProcess
                do
                        ficReport=$TMP/ficReport_$$.txt
                        rm $TMP/ficReport_*
                        ficEtat=$TMP/ETAT_ALIM_SIG_ARRET_TRAVAIL_$(date +%Y%m%d%H%M).csv
                        rm "$ficEtat"
                        export NUM_PROCESSUS=$numProcess
                        LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"
                        nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
                        listficReport=$(egrep -e "^$numProcess" "$ficReport")
                        export nb_erreur=$nb_erreur
                        echo "nombre evenement en erreur = $nb_erreur"
                        echo "Identifiant processus | Utilisateur | Type de signalement   | Total Evenements a traiter | Evenements traites | Evenements en erreur | Evenements deja pris en compte"
                        echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
                        echo "$listficReport"
                        rm $ficReport
                        LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"
                        if [ "$(wc -c < ${ficEtat})" -gt 221 ]
                        then
                        echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                        else
                        echo "${ficEtat} vide, donc non envoyé par mail"
                        fi
                done <  $TMP/dsnsig_at.txt
                if [  $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                fi
        fi
        ;;
SIGSYNCHR0FCT)
        # synchronisation FCT
[ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
        LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_fct.txt FIN_CONTRAT"
        if [ -s $TMP/dsnsig_fct.txt ]
        then
        LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_fct.txt"
        #generation rapport
                while read numProcess
                do
                        ficReport=$TMP/ficReport_$$.txt
                        rm $TMP/ficReport_*
                        ficEtat=$TMP/ETAT_ALIM_SIG_FIN_CONTRAT_$(date +%Y%m%d%H%M).csv
                        rm "$ficEtat"
                        export NUM_PROCESSUS=$numProcess
                        LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"
                        nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
                        listficReport=$(egrep -e "^$numProcess" "$ficReport")
                        export nb_erreur=$nb_erreur
                        echo "nombre evenement en erreur = $nb_erreur"
                        echo "Identifiant processus | Utilisateur | Type de signalement   | Total Evenements a traiter | Evenements traites | Evenements en erreur | Evenements deja pris en compte"
                        echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
                        echo "$listficReport"

                        rm $ficReport
                        LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"
                        if [ "$(wc -c < ${ficEtat})" -gt 221 ]
                        then
                        echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                        else
                        echo "${ficEtat} vide, donc non envoyé par mail"
                        fi
                done <  $TMP/dsnsig_fct.txt
                if [  $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                fi
        fi
        ;;
SIGSYNCHR0RA)
        # synchronisation RA
[ -z "${DATDEBUT}" ] && DATDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "ddmmyyyy" "-30d")
        LaunchEDSN "dsnsig:synchronize-signalement -d ${DATDEBUT} -x -o $TMP/dsnsig_ra.txt REPRISE_ARRET_TRAVAIL"
        if [ -s $TMP/dsnsig_ra.txt ]
        then
         LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_ra.txt"
        #generation rapport
                while read numProcess
                do
                        ficReport=$TMP/ficReport_$$.txt
                        rm $TMP/ficReport_*
                        ficEtat=$TMP/ETAT_ALIM_SIG_REPRISE_ARRET_TRAVAIL_$(date +%Y%m%d%H%M).csv
                        rm "$ficEtat"
                        export NUM_PROCESSUS=$numProcess
                        LaunchEDSN "dsnsig:list-synchronize-reports -o $ficReport"
                        nb_erreur=$(egrep -e "^$numProcess" "$ficReport" | awk '{print $11}')
                        listficReport=$(egrep -e "^$numProcess" "$ficReport")
                        export nb_erreur=$nb_erreur
                        echo "nombre evenement en erreur = $nb_erreur"
                        echo "Identifiant processus | Utilisateur | Type de signalement   | Total Evenements a traiter | Evenements traites | Evenements en erreur | Evenements deja pris en compte"
                        echo "----------------------+-------------+-----------------------+----------------------------+--------------------+----------------------+-------------------------------"
                        echo "$listficReport"

                        rm $ficReport
                        LaunchEDSN "dsnsig:download-synchronize-report $NUM_PROCESSUS $ficEtat"
                        if [ "$(wc -c < ${ficEtat})" -gt 221 ]
                        then
                        echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$ficEtat";OK" >> $TMP/CR_${S_CODSESS}.lst
                        else
                        echo "${ficEtat} vide, donc non envoyé par mail"
                        fi
                done <  $TMP/dsnsig_ra.txt
                if [  $nb_erreur -gt 0 ] && [ ! "${APPLI_CLIENT}" = "LCL" ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . Il y a $nb_erreur événements en erreur" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                fi

        fi
        ;;
SIGSYNCHR0ATOLD)
        # synchronisation AT
	LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_at.txt ARRET_TRAVAIL"
        if [ -s $TMP/dsnsig_at.txt ]
        then
	 LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_at.txt"
        fi
        ;;
SIGSYNCHR0FCTOLD)
        # synchronisation FCT
	LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_fct.txt FIN_CONTRAT"
        if [ -s $TMP/dsnsig_fct.txt ]
        then
	LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_fct.txt"
        fi
        ;;
SIGSYNCHR0RAOLD)
        # synchronisation RA
	LaunchEDSN "dsnsig:synchronize-signalement -x -o $TMP/dsnsig_ra.txt REPRISE_ARRET_TRAVAIL"
        if [ -s $TMP/dsnsig_ra.txt ]
        then
	 LaunchEDSN "dsnsig:wait-synchronize-signalement -w -1 @$TMP/dsnsig_ra.txt"
        fi
        ;;
SIGDOWNATT)
        PERIODE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm")
        LaunchEDSN "dsnsig:download-attachments ${PERIODE}"
;;
VALIDENVOI)
        #---  validation des envois periode en cours et precedente
        PERIODE=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymm")
	        echo "SELECT DISTINCT '##'||PERIODEDSN FROM DSNDECLARATION WHERE DSNMODE='PROD' AND PERIODEDSN <= '${PERIODE}';\nquit" | sqlplus -s ${EDSN_CONNECT_STRING} | grep "^##" | cut -c3- | sort -r | while read periode_paie
        do
        LaunchEDSN "dsnsend:list -x -o $TMP/liste_envois_${periode_paie}.txt ${periode_paie} NET"
        if [ -s $TMP/liste_envois_${periode_paie}.txt ]
        then
        LaunchEDSN "dsnsend:validate -f @$TMP/liste_envois_${periode_paie}.txt"
        fi
        done
        ;;
DELWFERR)
        #---- Suppression des processus en erreur
        LaunchEDSN "dsntest:cancel${newcmd}byprocessid ${newcmd221} -b -f @$TMP/DELWFERR_list_DSNERR_${Periode}.txt"
        ;;
ALIM)
        #---- Alimentation de la bande DSN Brouillon
	case $Slice in
	70)
        	# - Suppression de toutes les DSN de contrôle
        	LaunchEDSN "dsntest:cancel ${newcmd221} -m ALL -f ${Option} '${NomLance}' ${Periode}"
	;;
	*)
        	# - Suppression des DSN de contrôle non topées comme référence
        	LaunchEDSN "dsntest:cancel ${newcmd221} -m ALL -f -t NOTTESTED ${Option} '${NomLance}' ${Periode}"
	;;
	esac
        # - Création des DSN de contrôle (déclarations)
        LaunchEDSN "dsntest:create -x -o $TMP/ALIM_list_DSNId_${Periode}.txt -t ${TypeLance}${TypeLance2} ${newcmd400} -n Ctlr-${S_DATRAIT} ${Periode} '${NomLance}'${NomLance2}"
        LaunchEDSN "dsntest:wait-extraction${newcmd}byprocessid @$TMP/ALIM_list_DSNId_${Periode}.txt"
        ;;
PREGEN)
        #---- Prégénération de la bande DSN Brouillon
	rm -f $TMP/crefs.*
        # - Comparaison des DSN de contrôles générées avec les DSN topées comme référence
        LaunchEDSN "dsntest:comparerefs -o $TMP ${Option} '${NomLance}' ${Periode} crefs"
        LaunchEDSN "dsntest:list -t NOTTESTED -o $TMP/PREGEN_dsnCTL_NONREF_${Periode}.txt ${Option} '${NomLance}' ${newcmd400} ${Periode}"
        #recherche les dsn de ctrl identique aux ref à sup
        #     => Si la référence est identique à la DSN de contrôle générée, on supprime la DSN de contrôle générée
        #     => Si la référence est différente de la DSN de contrôle générée, on conserve la DSN de contrôle générée; la DSN de référence associée ne sera pas pris en compte pour générer la bande DSN
        cat $TMP/PREGEN_dsnCTL_NONREF_${Periode}.txt | awk -F"|" '{if(NR>2){print $4$5$6$8$9$10$11$2}}' > $TMP/PREGEN_dsnCTL_NONREF_cle_${Periode}.txt
        cat $TMP/crefs.ok | awk -F"|" '{if(NR>2){print $5$6$7$9$10$11$12}}' > $TMP/crefs.ok_cle.txt
        grep -f $TMP//crefs.ok_cle.txt $TMP/PREGEN_dsnCTL_NONREF_cle_${Periode}.txt | awk '{print $NF}' > $TMP/PREGEN_dsnctlasupp_${Periode}.txt
        cat $TMP/crefs.ko | awk -F"|" '{if(NR>2){print $2}}' | awk '{print $NF}' > $TMP/PREGEN_dsnrefasupp_${Periode}.txt
	#---- Modif 2016/01/09 : Problème sur le script de génération des DSN de contrôle : les espaces ne sont pas identiques
	#if [ -s $TMP/PREGEN_dsnctlasupp_${Periode}.txt ]
	if [ -s $TMP/crefs.clean ]
	then
		cat $TMP/crefs.clean | awk -F"|" '{if(NR>2){print $2}}' | sed "s/ *//g" > $TMP/PREGEN_dsnctlasupp_${Periode}.txt
        	LaunchEDSN "dsntest:cancel${newcmd}byprocessid ${newcmd221} -f @$TMP/PREGEN_dsnctlasupp_${Periode}.txt"
	fi
        ;;
GENE)        
        #---- Génération de la bande DSN Brouillon
        #On génère une bande DSN avec :
        # - Les déclarations de référence identiques aux DSN de contrôle (qui ont été supprimées)
        # - Les déclarations générées différentes des déclarations de référence
        # - Les déclarations générées sans déclaration de référence
	[ -z ${PFDSN} ] && PFDSN=pfdsn
        #LaunchEDSN "dsntest:list ${Option} '${NomLance}' -x -o $TMP/GENE_mesDSN_${NomLance}.txt ${newcmd400} ${Periode}"
        LaunchEDSN "dsntest:list ${Option} '${NomLance}' -x -o $TMP/GENE_mesDSN_${NomLance}.txt ${Periode}"
        grep -vf $TMP/PREGEN_dsnrefasupp_${Periode}.txt $TMP/GENE_mesDSN_${NomLance}.txt > $TMP/GENE_mesDSN_${NomLance}_${Periode}.txt
        LaunchEDSN "dsntest:gen-file -f ${PFDSN} @$TMP/GENE_mesDSN_${NomLance}_${Periode}.txt"
        ;;
GENEDEF)
        #---- Génération de la bande DSN Reel en remplacement de PMDSN4 avec M2M
        [ -z ${PFDSN} ] && PFDSN=pfdsn
        LaunchEDSN "dsn:list-declarations ${Option} '${NomLance}' -m PROD -x -o $TMP/GENEDEF_mesDSN_${NomLance}_${Periode}.txt ${Periode}"
        LaunchEDSN "dsn:gen-file -s ${PFDSN} @$TMP/GENEDEF_mesDSN_${NomLance}_${Periode}.txt"
        ;;
REINITCAMP)
	#---- Réinitialisation de la campagne pour un établissement
	#On extrait les déclarations
	CDETAB=$4
	LaunchEDSN "dsnworkflow:list-processes --etab ${CDETAB} -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
        LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt -t TBE ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:extract @$TMP/REINITCAMP_mesDSNTBE_${Periode}_${NomLance}.txt"
                LaunchEDSN "dsn:wait-extraction -f -e '${NomLance}' ${Periode}"
        fi
	#On valide les TBV 
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt -t TBV ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REINITCAMP_mesDSNVal_${Periode}_${NomLance}.txt"
        fi
	#On valide les TBC 
        LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -x -o $TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt -t TBC ${Periode}"
        if [ -s $TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/REINITCAMP_mesDSNValWithErr_${Periode}_${NomLance}.txt"
		sleep 5
        fi
	#On supprimes les déclarations 
	LaunchEDSN "dsn:delete-declarations ${Option} '${NomLance}' -f ${Periode}"
	;;
OUVC)
        #---- Ouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/OUVC_mesCampagne_${Periode}.txt ${Option} '${NomLance}' ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/OUVC_mesCampagne_${Periode}.txt"
        ;;
VERCAMP)
	#---- Vérification et recréation de la campagne
	LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/VERCAMP_mesUD_${NomLance}_${Periode}.txt ${Periode}"
	LaunchEDSN "dsn:list-tasks ${Option} '${NomLance}' -t TBE -o $TMP/VERCAMP_mesTask_${NomLance}_${Periode}.txt ${Periode}"
	REOUV=0
	#---- Boucle sur les établissements du pôle
	while read ligne
	do
		letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
		if [ $(grep -c "${letab}" $TMP/VERCAMP_mesTask_${NomLance}_${Periode}.txt) -eq 0 ]
		then
			REOUV=1
			#Extraire la déclaration de l'établissement
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt -e ${letab} -t TBE ${Periode}"
        		if [ -s $TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:extract @$TMP/VERCAMP_TBE_mesDSNVal_${letab}_${Periode}.txt"
                		LaunchEDSN "dsn:wait-extraction -e ${letab} ${Periode}"
        		fi
			#Valider les TBV de l'établissement concerné
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt -e ${letab} -t TBV ${Periode}"
        		if [ -s $TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:validate @$TMP/VERCAMP_mesDSNVal_${letab}_${Periode}.txt"
        		fi
			#Valider les TBC de l'établissement concerné
        		LaunchEDSN "dsn:list-tasks -x -o $TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt -e ${letab} -t TBC ${Periode}"
        		if [ -s $TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt ]
        		then
                		LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/VERCAMP_mesDSNValWithErr_${letab}_${Periode}.txt"
        		fi
			#Supprimer les déclarations de l'établissement concerné
			LaunchEDSN "dsn:delete-declarations -e ${letab} -f ${Periode}"
		fi
	done < $TMP/VERCAMP_mesUD_${NomLance}_${Periode}.txt
	if [ "$REOUV" = "1" ]
	then
		#réouvrir la campagne pour le pole concerné
		LaunchEDSN "campaign:list -t NOTOPEN ${Option} '${NomLance}' -x -o $TMP/VERCAMP_mesCampagne_${Periode}_${NomLance}.txt ${Periode}"
		LaunchEDSN "campaign:open ${Periode} @$TMP/VERCAMP_mesCampagne_${Periode}_${NomLance}.txt"
	fi
	;;
REOUVCAMP)
	#---- Réitialisation des campagnes
	#On reset les campagnes
        LaunchEDSN "dsnworkflow:list-processes -o $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313_a.txt ${Periode}"
        grep mensuelle $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313_a.txt|grep "Demarrage de l'alimentation"|cut -d" " -f1 > $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt
        if [ -s $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt ]
        then
                while read nuprocareinit
                do
                        echo "dsnworkflow:reset-process $nuprocareinit">$TMP/in_reset_${nuprocareinit}.txt
                        echo "oui" >>$TMP/in_reset_${nuprocareinit}.txt
                        echo "logout" >>$TMP/in_reset_${nuprocareinit}.txt
                        ./client <$TMP/in_reset_${nuprocareinit}.txt
                        rm $TMP/in_reset_${nuprocareinit}.txt
                done < $TMP/REOUVCAMP_mesCampagnes_reset_${Periode}_224_vers_313.txt
        fi
	LaunchEDSN "dsn:list-tasks -t TBE ${Periode}"
        #---- Réitialisation de la campagne 2.2.4 vers 3.1.3
        #On extrait les déclarations
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt -t TBE ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:extract @$TMP/REOUVCAMP_mesDSNTBE_${Periode}_224_vers_313.txt"
                LaunchEDSN "dsn:wait-extraction -f ${Periode}"
        fi
        #On valide les TBV
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt -t TBV ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REOUVCAMP_mesDSNVal_${Periode}_224_vers_313.txt"
        fi
        #On valide les TBC
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt -t TBC ${Periode}"
        if [ -s $TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors -w -1 @$TMP/REOUVCAMP_mesDSNValWithErr_${Periode}_224_vers_313.txt"
        fi
        #On supprimes les déclarations
        LaunchEDSN "dsn:delete-declarations -f ${Periode}"
	if [ "$REOUVCAMP" = "1" ]
	then
        	#---- Ouverture de la campagne
        	LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REOUVCAMP_mesCampagne_${Periode}_224_vers_313.txt -t NOTOPEN ${Periode}"
		LaunchEDSN "campaign:open ${Periode} @$TMP/REOUVCAMP_mesCampagne_${Periode}_224_vers_313.txt "
	fi
	;;
EXTR)
        #---- Extraction de la bande DSN Mensuelle
        # - Extraction de toutes les déclarations Mensuelle
        LaunchEDSN "dsn:list-tasks -x -o $TMP/EXTR_mesDSNTBE_${Periode}.txt -t TBE ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/EXTR_mesDSNTBE_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:extract @$TMP/EXTR_mesDSNTBE_${Periode}.txt"
        	LaunchEDSN "dsn:wait-extraction ${Option} '${NomLance}' ${Periode}"
		sleep 300
	fi
        # - Validation automatique des déclarations mensuelles identiques aux DSn de contrôle topées en "référence"
        LaunchEDSN "dsn:list-tasks -r REFOK -t TBV -x -o $TMP/EXTR_mesDSNPREVAL_${Periode}.txt ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/EXTR_mesDSNPREVAL_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate @$TMP/EXTR_mesDSNPREVAL_${Periode}.txt"
	fi
        ;;

REPEXTR2)
        #---- Reprise de l'Extraction de la bande DSN Mensuelle
        #---- Validation des déclarations à l'état TBV
        LaunchEDSN "dsnworkflow:list-processes -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
        LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done
;;
REPEXTR)
        #---- Reprise de l'Extraction de la bande DSN Mensuelle
        #---- Validation des déclarations à l'état TBV
	LaunchEDSN "dsnworkflow:list-processes -t monthlyDeclaration -x -o $TMP/liste_decl_mensuelle_${Periode}.txt ${Periode}"
        cat $TMP/liste_decl_mensuelle_${Periode}.txt|while read nuproc
        do
	LaunchEDSN "dsnworkflow:cancel-declarant $nuproc"
        done 
  
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REPEXTR_mesDSNVAL_${Periode}.txt -t TBV ${Periode}"
        if [ -s $TMP/REPEXTR_mesDSNVAL_${Periode}.txt ]
        then
                LaunchEDSN "dsn:validate @$TMP/REPEXTR_mesDSNVAL_${Periode}.txt"
        fi
        #---- Forçage de validation des déclarations à l'état TBC
        LaunchEDSN "dsn:list-tasks -x -o $TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt -t TBC ${Periode}"
        if [ -s $TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt ]
        then
                LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/REPEXTR_mesDSNValWithErr_${Periode}.txt"
        fi
        #---- Supprimer les déclarations
	LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPEXTR_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPEXTR_mesCampagne_${Periode}.txt"
        ;;
CTLV)
        #---- Controle et Validation de la bande DSN Mensuelle
        # Cette étape valide automatiquement toutes les DSN mensuelles non validée (à valider + à corriger)
        # Obligatoire afin d'avoir toutes les déclarations dans les bandes mensuelles (EDSN n.extrait les déclarations dans une bande que si elles sont à l.état valide)
        LaunchEDSN "dsn:list-tasks -x -o $TMP/CTLV_mesDSNVal_${Periode}.txt -t TBV ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/CTLV_mesDSNVal_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate @$TMP/CTLV_mesDSNVal_${Periode}.txt"
	fi
        LaunchEDSN "dsn:list-tasks -x -o $TMP/CTLV_mesDSNValWithErr_${Periode}.txt -t TBC ${Option} '${NomLance}' ${Periode}"
	if [ -s $TMP/CTLV_mesDSNValWithErr_${Periode}.txt ]
	then
        	LaunchEDSN "dsn:validate-with-errors ${newcmd221} @$TMP/CTLV_mesDSNValWithErr_${Periode}.txt"
	fi
        ;;
REPCTLV)
        #---- Reprise du Controle et Validation de la bande DSN Mensuelle
        #---- Supprimer les déclarations
	LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPCTLV_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPCTLV_mesCampagne_${Periode}.txt"
	;;
ENVP)
        #---- Préparation et constitution de l'envoi de la bande DSN Mensuelle (selon découpage)
        # Cette étape génère la bande DSN Mensuelle
	#sleep 120
        LaunchEDSN "dsnsend:list-pendingdeclarations ${Option} '${NomLance}' -x -o $TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt ${Periode}"
        #if [ $edsnversion -gt 350 ]
        if [ $(echo "${edsnversion}" | cut -c1-3) -gt 350 ]
        then
		LaunchEDSN "dsnsend:prepare -g superviseurPole(${NomLance}) -x -o $TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt @$TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt"
        else
		LaunchEDSN "dsnsend:prepare -u ${Gestionnaire} -x -o $TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt @$TMP/ENVP_mesDSNEnv_${NomLance}_${Periode}.txt"
        fi
        LaunchEDSN "dsnsend:wait-preparation @$TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt"
        mkdir -p $TMP/${NomLance}_${Periode}
        LaunchEDSN "dsnsend:do-send $TMP/${NomLance}_${Periode} @$TMP/ENVP_mesEnvois_${NomLance}_${Periode}.txt"
        #Copie des fichiers produits pour la sauvegarde légale
        mkdir -p $SIGACS/SauvegardeLegale/DSN/
        cp $TMP/${NomLance}_${Periode}/* $SIGACS/SauvegardeLegale/DSN/
        ;;
ANNENVP)
	#---- Annulation des Envois de DSN Mensuelle
        #---- Liste des Envois NET
        LaunchEDSN "dsnsend:list -x -o $TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt ${Periode} NET"
        #---- Annuler les Envois NET
        if [ -s $TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/ANNENVP_liste_annulation_envoi_${Periode}_NET.txt"
        fi
        #---- Liste des Envois MSA
        LaunchEDSN "dsnsend:list -x -o $TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt ${Periode} MSA"
        if [ -s $TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/ANNENVP_liste_annulation_envoi_${Periode}_MSA.txt "
        fi
        ;;
REPENVP)
        #---- Reprise de la Préparation et constitution de l'envoi de la bande DSN Mensuelle (selon découpage)
        #---- Liste des Envois NET
        LaunchEDSN "dsnsend:list -x -o $TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt ${Periode} NET"
        #---- Annuler les Envois NET
        if [ -s $TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/REPENVP_liste_annulation_envoi_${Periode}_NET.txt"
        fi
        #---- Liste des Envois MSA
        LaunchEDSN "dsnsend:list -x -o $TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt ${Periode} MSA"
        if [ -s $TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt ]
        then
               LaunchEDSN "dsnsend:unvalidate -f @$TMP/REPENVP_liste_annulation_envoi_${Periode}_MSA.txt "
        fi
        #---- Supprimer les déclarations sinon rien ne sera à réouvrir
        LaunchEDSN "dsn:delete-declarations -f ${Periode}"
        #---- Réouverture de la campagne
        LaunchEDSN "campaign:list -t NOTOPEN -x -o $TMP/REPENVP_mesCampagne_${Periode}.txt ${Periode}"
        LaunchEDSN "campaign:open ${Periode} @$TMP/REPENVP_mesCampagne_${Periode}.txt"
	;;
ENVV)
        #---- Passage des DSN Mensuelle en attente de conformité
        LaunchEDSN "dsnsend:validate -f @$TMP/ENVP_mesEnvois_${Periode}.txt"
        ;;
LSTDECLMENS)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations -x -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations -x -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLMENS_CRM)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations -x -o $TMP/TOTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
                LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}.txt"
        else
                LaunchEDSN "dsn:list-declarations -x -o $TMP/TOTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
                LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}_${NomLance}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt"
        fi
	LaunchEDSN "m2m:list-declarations-from-crm -x -o $TMP/LSTDECLMENS_CRM_mesdeclarations_${Periode}.txt 94 ${Periode} @${TMP}/TOTDECLMENS_mesdeclarations_${Periode}.txt"
        ;;
LSTDECLPAS)
        #---- Liste des déclarations de type Mensuel pour CRM PAS DGFIP
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations --last-version-only -x -o $TMP/LSTDECLPAS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations --last-version-only -x -o $TMP/LSTDECLPAS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLMENSX)
        #---- Liste des déclarations de type Mensuel
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsn:list-declarations  -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsn:list-declarations  -o $TMP/LSTDECLMENS_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
LSTDECLCTRL)
        #---- Liste des déclarations de type Brouillon
        if [ "${NomLance}" = "" ]
        then
                LaunchEDSN "dsntest:list -x -o $TMP/LSTDECLCTRL_mesdeclarations_${Periode}.txt ${Periode}"
        else
                LaunchEDSN "dsntest:list -x -o $TMP/LSTDECLCTRL_mesdeclarations_${Periode}_${NomLance}.txt ${Option} '${NomLance}' ${Periode}"
        fi
        ;;
QRYCTRL)
        #---- Exécution d'1 rapport pour les déclarations de type Brouillon
        cmdFORMAT=
        case "$NOMFIC" in
        	*.xlsx)
        		cmdFORMAT="-f XLSX"
        		;;
        	*.xml)
        		cmdFORMAT="-f XML"
        		;;
        esac
        LaunchEDSN "dsntest:dsnreport $cmdFORMAT -o $TMP/${NOMFIC} ${RAPPORT} @${FICDECL}"
	;;
QRYMENS)
        #---- Exécution d'1 rapport pour les déclarations de type Mensuel
        cmdFORMAT=
        case "$NOMFIC" in
        	*.xlsx)
        		cmdFORMAT="-f XLSX"
        		;;
        	*.xml)
        		cmdFORMAT="-f XML"
        		;;
        esac
        LaunchEDSN "dsn:dsnreport $cmdFORMAT -o $TMP/${NOMFIC} ${RAPPORT} @${FICDECL}"
        ;;
QRYMENS331)
        #---- Exécution d'1 rapport pour les déclarations de type Mensuel (Uniquement pour palier au bug de la Version 3.3.1)
        while read numdec
        do
                LaunchEDSN "dsn:dsnreport -o $TMP/ANO_CORR_${RAPPORT}_${numdec}_$$_${Periode}.csv ${RAPPORT} ${numdec}"
        done < ${FICDECL}
        #---- Concaténation
        pfichier=""
        ls -1 $TMP/ANO_CORR_${RAPPORT}_*_$$_${Periode}.csv|while read numfic
        do
                if [ "$pfichier" = "" ]
                then
                        pfichier="X"
                        cat $numfic > $TMP/${NOMFIC}
                else
                        awk '{if (NR>1){print $0;}}' $numfic >> $TMP/${NOMFIC}
                fi
        done
        rm $TMP/ANO_CORR_${RAPPORT}_*
        ;;
LSTDECLSIG)
        #---- Liste des déclarations de type Signalement
                        LaunchEDSN "dsnsig:list-declarations -x -o $TMP/LSTDECLSIG_mesSignalements_${RAPPORT}_${Periode}.txt ${Periode} ${RAPPORT}"
            ;;
QRYSIG)
        #---- Exécution d'1 rapport pour les signalements [SIGNALEMENT_FIN_CONTRAT, SIGNALEMENT_ARRET_TRAVAIL, SIGNALEMENT_REPRISE_ARRET_TRAVAIL]
        LaunchEDSN "dsnsig:dsnreport -o $TMP/${NOMFIC} signalement @${FICDECL}"
        ;;
PRODCRM)
	#---- Generation fichier taux du pas a partir des retours dgfip fournis par le client
        if [ $(echo "${edsnversion}" | cut -c1) -lt 5 ]
        then
        LaunchEDSN "admin:produce-crm ${REPCIBLE}"
        else
        LaunchEDSN "pas:extract-from-net-e-files -f XML -fn ${NOMFIC} ${REPCIBLE} ${DATE} ${OUTCIBLE}"
        grep "KO" $FILE/traitements.txt | cut -c1-36 > $FILE/traitementsKO.txt
        rm $FILE/detail-traitements-KO.txt
         if [ -s $FILE/traitementsKO.txt ]
         then
        #boucle sur ces identifiants KO
                 while read ident
                 do
                 LaunchEDSN "DSN:DETAIL-DECLARATION $ident"
                 done < $FILE/traitementsKO.txt
          cp ${FICLOG} $FILE/detail-traitements-KO.txt
          echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$FILE/detail-traitements-KO.txt";OK" >> $TMP/CR_${S_CODSESS}.lst
         fi
        fi
	;;
PASM2M)
        # generation fichier taux du Pas
        LaunchEDSN "pas:extract-from-m2m-decl -f XML -fn ${NOMFIC} ${OUTCIBLE} @${FICDECL}"
        rm $FILE/detail-traitements-KO.txt
        grep "KO" $FILE/traitements.txt | cut -c1-36 > $FILE/traitementsKO.txt
        if [ -s $FILE/traitementsKO.txt ]
         then
        #boucle sur ces identifiants KO
                 while read ident
                 do
                 LaunchEDSN "DSN:DETAIL-DECLARATION $ident"
                 done < $FILE/traitementsKO.txt
          cp ${FICLOG} $FILE/detail-traitements-KO.txt
          echo ${S_PROCEXE}";"${Description}";"${DATDEB}";"${DEBTRT}";"$FILE/detail-traitements-KO.txt";OK" >> $TMP/CR_${S_CODSESS}.lst
         fi
        ;;
REPM2MERR)
        LaunchEDSN "dsnworkflow:list-m2m-send-error-processes -x -o $TMP/CTL_list_M2MERR_${Periode}.txt ${Periode}"
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
        LaunchEDSN "dsnworkflow:retry-erroneous-processes @$TMP/CTL_list_M2MERR_${Periode}.txt"
        fi
        ;;
REPM2MERR2)
        #reprise des depots mensuels et signalement a faire
        RCGPO=0
        for typeEnvoi in m2m m2mSignalement
        do
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $TMP/CTL_list_M2MERR_${Periode}.txt -t $typeEnvoi" </dev/null
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
                LaunchEDSN "dsnworkflow:retry-erroneous-processes @$TMP/CTL_list_M2MERR_${Periode}.txt" </dev/null
                # Liste des depots M2M
                LaunchEDSN "async:list-jobs -x -o $TMP/CTL_list_job1_${Periode}.txt -t $typeEnvoi" </dev/null
                if [ -s $TMP/CTL_list_job1_${Periode}.txt ]
                then
                n1=$(wc -l $TMP/CTL_list_job1_${Periode}.txt)
                echo "Il y a $n1 reprise de depot $typeEnvoi a traiter."
                LaunchEDSN "async:wait-jobs @$TMP/CTL_list_job1_${Periode}.txt" </dev/null
                rm $TMP/CTL_list_job1_${Periode}.txt
                echo "Reprises $typeEnvoi effectuees"
                fi
        else
                echo "Aucune reprise de depot $typeEnvoi a faire"
        fi

         #post controle des reprises non faites
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $TMP/CTL_list_M2MERR_${Periode}.txt -t $typeEnvoi" </dev/null
        if [ -s $TMP/CTL_list_M2MERR_${Periode}.txt ]
        then
                echo "Il reste des reprises $typeEnvoi a realiser."
                LaunchEDSN "dsnworkflow:list-processes -e -t $typeEnvoi" </dev/null
            RCGPO=`echo $(($RCGPO + $RC + 1 ))`
        fi
        done

        if [ $RCGPO -gt 0 ]
        then
                $SRVNET_DIR/U_POST_UPROC_${Slice} "KO reprise en échec ou incomplète" "$DATDEB" $DEBTRT "$Description"
        fi
;;
VERIFCOMPTE)
        LaunchEDSN "m2m:list-declarants -o $TMP/listedecl.txt"
        while read line
        do
                actif=$(echo $(echo "$line" | awk -F '|' '{print $2}'))
                habilitation=$(echo $(echo "$line" | awk -F '|' '{print $6}'))
                IdCompteDeclarant=$(echo $(echo "$line" | awk -F '|' '{print $1}'))
        echo "actif=$actif"
        echo "IdCompteDeclarant=$IdCompteDeclarant"
        echo "habilitation=$habilitation"
                if [ $actif = "x" ]
                then
                        if [ $habilitation = "NET / MSA" ]
                        then
                        #gestion particuliere point depot NET / MSA : on doit donc tester les 2
                                #test compte point depot NET
                                LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' NET"
                                #test compte point depot MSA
                                LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' MSA"
                        else
                        #test compte pour point depot correspondant
                        LaunchEDSN "m2m:ping-gip -d '$IdCompteDeclarant' $habilitation"
                        fi
                fi
        done < $TMP/listedecl.txt
;;
DOWNLOADCRM)
		#---- récupération des retours M2M
		if [ -z ${LISTRETOUR} ]
   		then
        LaunchEDSN "DSN:DOWNLOAD-ATTACHMENTS -p ${CDPOLE} -x ${PREFIXNMFIC} ${PERIODE}"
		else
        LaunchEDSN "DSN:DOWNLOAD-ATTACHMENTS -p ${CDPOLE} -x ${PREFIXNMFIC} -n ${LISTRETOUR} ${PERIODE}"
        fi		
	;;
RELOADCRM94)
		#rechargement CRM94 suite passage DSN V5
	LaunchEDSN "dsn:list-declarations -x -o $TMP/liste_CRM94_${Periode}.txt ${Periode}"
	 if [ -s $TMP/liste_CRM94_${Periode}.txt ]
	 then
		LaunchEDSN "dsn:delete-attachments @$TMP/liste_CRM94_${Periode}.txt"
        	cat $TMP/liste_CRM94_${Periode}.txt|while read nuproc
		do
		LaunchEDSN "m2m:get-gipdocuments-for-declaration $nuproc"
                LaunchEDSN "async:list-jobs -x -o $TMP/Jobs1.txt -t m2mAttachmentDgfipIntegration"
                if [ -s $TMP/Jobs1.txt ]
                then
                LaunchEDSN "async:wait-jobs @$TMP/Jobs1.txt"
		rm $TMP/Jobs1.txt
		fi
		
		LaunchEDSN "async:list-jobs -x -o $TMP/Jobs2.txt -t m2mAttachmentIntegration"
                if [ -s $TMP/Jobs2.txt ]
		then
                LaunchEDSN "async:wait-jobs @$TMP/Jobs2.txt"
		rm $TMP/Jobs2.txt
		fi
                done
	 else 
	 echo "aucun CRM 94 listé en BDSN pour ${Periode}"
	 fi	
	 ;;
INTREPVERS)
        # integration repartition des versements
        #
                GetVarProfile dsnconf
                DSNCONF=${PROFVAR}
        # Liste les declarations DSN stockees en BDSN pour la periode
        LaunchEDSN "dsn:list-declarations -x -o $TMP/liste_DSN_${Periode}.txt ${Periode}"
        if [ -s $TMP/liste_DSN_${Periode}.txt ]
        then

                if [ ! -f ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg ] || ! grep -q "^paiement.organismes=" ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg
                then
                export listorg="DGFIP"
                else
                listorg=$(grep "^paiement.organismes=" ${DSNCONF}/com.soprahr.edsn.paiement.impl.cfg | cut -d '=' -f2 | sed "s:,:\n:g")
                export listorg
                fi
                echo "listorg=$listorg"

                echo "$listorg" | while read nmorg
                do
                echo $nmorg
                        # Filtre les declarations candidates a la repartition des versements pour DGFIP
                        LaunchEDSN "dsn:list-repartition-candidates -x -o $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt ${Periode} ${nmorg} @$TMP/liste_DSN_${Periode}.txt"
                        if [ -s $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt ]
                        then


                        # lancement de la repartition des versements pour l'organisme DGFIP
                        LaunchEDSN "dsn:start-repartition-versements -x -o $TMP/liste_wait-repartition_${Periode}.txt ${Periode} ${nmorg} @$TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt"
			LaunchEDSN "dsn:wait-repartition-versements @$TMP/liste_wait-repartition_${Periode}.txt"	
                        while read iddecl
                        do
                        LaunchEDSN "dsn:status-repartition-versements ${iddecl}"
                        done < $TMP/liste_candidates_DSN_${nmorg}_${Periode}.txt
                        else
                        echo "rien a traiter pour ${nmorg}."
                        fi
                done
        fi
        ;;
SCANAERFCTUALL)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
SCANAERFCTU)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
SCANAER2FCTU)
                #---- Recuperation des retours KO AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                export FICZIP=$TMP/SIGNFCTU_KO_RAER_${DATEDEBUT}.zip

                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour en erreur AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm ${FICZIP}
                fi
        ;;
COLLECTFCTUOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par etablissement
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                while read ligne
                do
                letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
                case "${Slice}" in
                "AL")
                        export FICZIP=$TMP/AERFCTU_${letab}_${DATEFIN}.zip
                ;;
                *)
                        export FICZIP=$TMP/SIGNFCTU_${letab}_RAER_${DATEDEBUT}.zip
                ;;
		esac
                echo "======================================================================="
                echo "****** debut traitement etablissement ${letab} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -e ${letab} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${letab} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${letab} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTUD_${NomLance}_${Periode}.txt
        #       $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        ;;
COLLECTFCTUKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par etablissement
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                while read ligne
                do
                letab="$(echo "$ligne" | awk -F"/" '{print $1}')"
                export FICZIP=$TMP/SIGNFCTU_KO_${letab}_RAER_${DATEDEBUT}.zip

                echo "======================================================================="
                echo "****** debut traitement etablissement ${letab} `date +'%H:%M:%S'` ******"
                echo "======================================================================="
                LaunchEDSN "m2m:scan-returns -e ${letab} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                                                then
                        echo "${letab} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${letab} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTUD_${NomLance}_${Periode}.txt
#               $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
        ;;
SCANAERFCTUOLD)
                #---- Recuperation des retours AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")
                echo "Slice=$Slice"
                echo "PAYS=$PAYS"
                echo "CODEUG=${S_CODUG}"
                export FICZIP=$TMP/SIGNFCTU_RAER_${DATEDEBUT}.zip
                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm $FICZIP
                fi
        ;;
SCANAER2FCTUOLD)
                #---- Recuperation des retours KO AER 44 des signalements FCTU
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")
                export FICZIP=$TMP/SIGNFCTU_KO_RAER_${DATEDEBUT}.zip
                LaunchEDSN "m2m:scan-returns ${Option} '${NomLance}' -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description"
                else
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK . aucun retour en erreur AER FCTU" "$DATDEB" $DEBTRT "$Description"
                        rm $FICZIP
                fi
        ;;
COLLECTFCTUSOCOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
		while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/AERFCTU_${ste}_${DATEFIN}.zip
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -ste ${ste} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${ste} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $ste"
		else
                        echo "${ste} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
;;
COLLECTFCTUSOCKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
                while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/AERFCTUKO_${ste}_${DATEFIN}.zip
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="

                LaunchEDSN "m2m:scan-returns -ste ${ste} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                then
                        echo "${ste} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $ste"
                else
                        echo "${ste} : le fichier ${FICZIP} est vide donc supprimÃ©"
                        rm ${FICZIP}
                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
;;
COLLECTFCTUCLEOK)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")

                
                if [ -s $TMP/LISTCLE_${NomLance}_${Periode}.txt ]; then
			while read ligne
			do
				CLE="$(echo "$ligne")"
				export FICZIP=$TMP/AERFCTU_${CLE}_${DATEFIN}.zip
				echo "======================================================================="
				echo "****** debut traitement etablissement ${CLE} `date +'%H:%M:%S'` ******"
				echo "======================================================================="

				LaunchEDSN "m2m:scan-returns -afas1 ${CLE} -k -n 44 -s OK -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
				if [ "$(wc -c < ${FICZIP})" -gt 311 ]
				then
					echo "${CLE} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©"
					$SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $CLE"
				else
					echo "${CLE} : le fichier ${FICZIP} est vide donc supprimÃ©"
					rm ${FICZIP}
				fi
			done < $TMP/LISTCLE_${NomLance}_${Periode}.txt
		else
			echo "Liste Cle d'affectation non extraite"
			$SRVNET_DIR/U_POST_UPROC_${Slice} "KO" "$DATDEB" $DEBTRT "$Description"
		fi
;;
COLLECTFCTUCLEKO)
                #---- Recuperation des retours AER 44 des signalements FCTU par societe
                DATEDEBUT=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd" "-1d")
                DATEFIN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")


                if [ -s $TMP/LISTCLE_${NomLance}_${Periode}.txt ]; then
                        while read ligne
                        do
                                CLE="$(echo "$ligne")"
                                export FICZIP=$TMP/AERFCTUKO_${CLE}_${DATEFIN}.zip
                                echo "======================================================================="
                                echo "****** debut traitement etablissement ${CLE} `date +'%H:%M:%S'` ******"
                                echo "======================================================================="

                                LaunchEDSN "m2m:scan-returns -afas1 ${CLE} -k -n 44 -s KO -dp ${DATEDEBUT} -dpstop ${DATEFIN} Signalement ${FICZIP}"
                                if [ "$(wc -c < ${FICZIP})" -gt 311 ]
                                then
                                        echo "${CLE} : le fichier ${FICZIP} a Ã©tÃ© produit et dÃ©posÃ©" dÃ©p
                                        $SRVNET_DIR/U_POST_UPROC_${Slice} "OK" "$DATDEB" $DEBTRT "$Description $CLE"
                                else
                                        echo "${CLE} : le fichier ${FICZIP} est vide donc supprimÃ©"u
                                        rm ${FICZIP}
                                fi
                        done < $TMP/LISTCLE_${NomLance}_${Periode}.txt
                else
                        echo "Liste Cle d'affectation non extraite"
                        $SRVNET_DIR/U_POST_UPROC_${Slice} "KO" "$DATDEB" $DEBTRT "$Description"
                fi
;;
SYNCCRM)
                Periode=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm")
                DATEDEBUT=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm" "-1m")
                DATEFIN=$($UXEXE/uxdat "yyyymm" "${S_DATRAIT}" "yyyymm" "-1m" )
                LaunchEDSN "dsn:list-ud ${Option} '${NomLance}' -x -o $TMP/LISTUD_${NomLance}_${Periode}.txt ${Periode}"
                cat $TMP/LISTUD_${NomLance}_${Periode}.txt | cut -c5-7 | sort -u > $TMP/LISTSTE_${NomLance}_${Periode}.txt
                while read ligne
                do
                ste="$(echo "$ligne")"
                        export FICZIP=$TMP/ATT_${ste}_${Periode}.csv
                echo "======================================================================="
                echo "****** debut traitement etablissement ${ste} `date +'%H:%M:%S'` ******"
                echo "======================================================================="
                LaunchEDSN "atmp:sync-referential-from-m2m --output sync.log --verbose --output-ref /pr121/txt/tmp/ATT_${ste}_${Periode}.csv --ignore-current-ref --type-perimeter ENT ${DATEDEBUT} ${DATEFIN} ${ste}"
                if [ $? -eq 0 ]
                then
                        echo "${ste} : le fichier /pr121/txt/tmp/ATT_${ste}_${Periode}.csv a Ã©tÃ© produit et dÃ©posÃ©"
                else
                        echo "${ste} : le fichier /pr121/txt/tmp/ATT_${ste}_${Periode}.csv est vide donc supprimÃ©"

                fi
                done < $TMP/LISTSTE_${NomLance}_${Periode}.txt
;;
esac
exit $?
