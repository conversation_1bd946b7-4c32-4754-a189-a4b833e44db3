#!/bin/ksh
#===============================================================================
# Script d'analyse des dépassements de capacité pour la paie
#===============================================================================
# Description : Analyse les fichiers de log pour détecter les dépassements de
#               capacité lors des traitements de paie
# Auteur      : Elmokhtar BENFRAJ
# Date        : 13/04/2021
# Version     : 2.0 - Restructuré et amélioré
#
# Paramètres :
#   $1 : Nom du fichier de log à analyser (dans le répertoire $LOG)
#
# Variables d'environnement requises :
#   LOG, TMP, DEMANDE
#
# Utilisation :
#   ./Check_depordement.ksh <FICHIER_LOG>
#
# Variables d'environnement optionnelles pour filtrage :
#   IGNORE_DEPASSEMENTS : 1 pour ignorer les dépassements de capacité (défaut: 0)
#   IGNORE_DEBORDEMENTS : 1 pour ignorer les messages de débordement (défaut: 0)
#   IGNORE_CODES : Liste des codes d'information à ignorer (séparés par des virgules)
#   IGNORE_MATRICULES : Liste des matricules à ignorer (séparés par des virgules)
#   SEUIL_DEPASSEMENTS : Nombre minimum de dépassements pour déclencher une alerte (défaut: 1)
#   SEUIL_DEBORDEMENTS : Nombre minimum de débordements pour déclencher une alerte (défaut: 1)
#   IGNORE_ERRORS : 1 pour ignorer les erreurs générales (défaut: 1)
#   IGNORE_WARNINGS : 1 pour ignorer les avertissements (défaut: 0)
#
# Variables de génération de rapports :
#   GENERATE_PDF : 1 pour générer un rapport PDF (défaut: 0)
#   GENERATE_XLSX : 1 pour générer un fichier Excel XLSX (défaut: 0)
#   REPORT_DIR : Répertoire de sortie des rapports (défaut: $TMP)
#   REPORT_PREFIX : Préfixe des noms de fichiers (défaut: "rapport_paie")
#
# Exemples :
#   ./Check_depordement.ksh FFCALK3W_RJ2.2925
#   ./Check_depordement.ksh PAIE_202312.log
#   export IGNORE_CODES="ZYWA,TEST"; ./Check_depordement.ksh LOG.txt
#   export SEUIL_DEPASSEMENTS=3; ./Check_depordement.ksh LOG.txt
#   export GENERATE_PDF=1 GENERATE_XLSX=1; ./Check_depordement.ksh LOG.txt
#
# Historique :
#   13/04/2021 - ebenfraj - Création initiale
#   $(date +%d/%m/%Y) - TechOps - Restructuration et amélioration
#===============================================================================

#===============================================================================
# CONFIGURATION ET INITIALISATION
#===============================================================================

# Fonction d'aide
show_usage() {
    echo "Usage: $0 <FICHIER_LOG>"
    echo ""
    echo "Paramètres:"
    echo "  FICHIER_LOG  : Nom du fichier de log à analyser (dans \$LOG)"
    echo ""
    echo "Variables d'environnement requises:"
    echo "  LOG    : Répertoire des fichiers de log"
    echo "  TMP    : Répertoire temporaire"
    echo "  DEMANDE: Nom de la demande en cours"
    echo ""
    echo "Exemples:"
    echo "  $0 FFCALK3W_RJ2.2925"
    echo "  $0 PAIE_202312.log"
}

# Validation des paramètres
if [ -z "$1" ]; then
    echo "ERREUR: Nom du fichier de log manquant" >&2
    show_usage
    exit 1
fi

# Validation des variables d'environnement
if [ -z "$LOG" ]; then
    echo "ERREUR: Variable d'environnement LOG non définie" >&2
    exit 1
fi

if [ -z "$TMP" ]; then
    echo "ERREUR: Variable d'environnement TMP non définie" >&2
    exit 1
fi

# Configuration des chemins
Fich_Log="$LOG/$1"
nomFich_Log="$1"

# Validation de l'existence du fichier de log
if [ ! -f "$Fich_Log" ]; then
    echo "ERREUR: Fichier de log introuvable: $Fich_Log" >&2
    exit 1
fi

# Changement vers le répertoire temporaire
cd "$TMP" || {
    echo "ERREUR: Impossible d'accéder au répertoire temporaire: $TMP" >&2
    exit 1
}

# Variables de temps
DATDEB=$(date +'%H:%M:%S')
DEBTRT=$(date +'%s')

#===============================================================================
# CONFIGURATION DES FILTRES D'ERREURS
#===============================================================================

# Variables de filtrage avec valeurs par défaut
IGNORE_DEPASSEMENTS=${IGNORE_DEPASSEMENTS:-0}
IGNORE_DEBORDEMENTS=${IGNORE_DEBORDEMENTS:-0}
IGNORE_CODES=${IGNORE_CODES:-""}
IGNORE_MATRICULES=${IGNORE_MATRICULES:-""}
SEUIL_DEPASSEMENTS=${SEUIL_DEPASSEMENTS:-1}
SEUIL_DEBORDEMENTS=${SEUIL_DEBORDEMENTS:-1}
IGNORE_ERRORS=${IGNORE_ERRORS:-1}
IGNORE_WARNINGS=${IGNORE_WARNINGS:-0}

# Variables de génération de rapports
GENERATE_PDF=${GENERATE_PDF:-0}
GENERATE_XLSX=${GENERATE_XLSX:-0}
REPORT_DIR=${REPORT_DIR:-$TMP}
REPORT_PREFIX=${REPORT_PREFIX:-"rapport_paie"}

# Fonction pour vérifier si un code doit être ignoré
should_ignore_code() {
    local code="$1"
    if [ -n "$IGNORE_CODES" ]; then
        echo "$IGNORE_CODES" | grep -q "\b$code\b"
        return $?
    fi
    return 1
}

# Fonction pour vérifier si un matricule doit être ignoré
should_ignore_matricule() {
    local matricule="$1"
    if [ -n "$IGNORE_MATRICULES" ]; then
        echo "$IGNORE_MATRICULES" | grep -q "\b$matricule\b"
        return $?
    fi
    return 1
}

echo "==============================================================================="
echo "Analyse des dépassements de capacité - Paie"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Taille du fichier: $(wc -c < "$Fich_Log") octets"
echo "Heure début     : $DATDEB"
echo ""
echo "Configuration des filtres :"
echo "  Ignorer dépassements     : $([ "$IGNORE_DEPASSEMENTS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Ignorer débordements     : $([ "$IGNORE_DEBORDEMENTS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Seuil dépassements       : $SEUIL_DEPASSEMENTS"
echo "  Seuil débordements       : $SEUIL_DEBORDEMENTS"
echo "  Codes ignorés            : ${IGNORE_CODES:-Aucun}"
echo "  Matricules ignorés       : ${IGNORE_MATRICULES:-Aucun}"
echo "  Ignorer erreurs générales: $([ "$IGNORE_ERRORS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Ignorer avertissements   : $([ "$IGNORE_WARNINGS" = "1" ] && echo "OUI" || echo "NON")"
echo ""
echo "Configuration des rapports :"
echo "  Génération PDF           : $([ "$GENERATE_PDF" = "1" ] && echo "OUI" || echo "NON")"
echo "  Génération XLSX          : $([ "$GENERATE_XLSX" = "1" ] && echo "OUI" || echo "NON")"
echo "  Répertoire de sortie     : $REPORT_DIR"
echo "  Préfixe des fichiers     : $REPORT_PREFIX"
echo "==============================================================================="

#===============================================================================
# EXTRACTION DES INFORMATIONS DU FICHIER DE LOG
#===============================================================================

echo ">>> Extraction des informations de traitement..."

# Extraction des informations principales
NUMERO_TRAVAIL=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $3}')
ZOCODE=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $5}')

# Extraction des paramètres de paie
PERPAI=$(grep -a "DAV-00000000-DA18A" "${Fich_Log}" | cut -c27-32)
TYPPAI=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c37-37)
LIMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c48-51)
RAPGEN=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c42-45)
TEMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c39-39)

# Informations optionnelles (commentées dans la version originale)
DOSEXT=$(grep -a "BMQ-BBAM0021" "${Fich_Log}" | cut -c51-59 | head -1)
DOSSOR=$(grep -a "DBA-BBAD0009-EMPLOYEES. (WRITE)" "${Fich_Log}" | cut -c46-54)
NBNUDOS=$(grep -a "DBA-BBAD0009-PAY DOSS.. (WRITE)" "${Fich_Log}" | cut -c46-54)

#===============================================================================
# AFFICHAGE DES INFORMATIONS EXTRAITES
#===============================================================================

echo ""
echo ">>> Informations du traitement de paie"
echo "---------------------------------------"
echo "Demande           : ${DEMANDE:-Non définie}"
echo "Code zone         : ${ZOCODE:-Non trouvé}"
echo "Numéro de travail : ${NUMERO_TRAVAIL:-Non trouvé}"
echo "Fichier analysé   : ${nomFich_Log}"
echo ""
echo ">>> Paramètres de paie"
echo "----------------------"
echo "Période de paie                        : ${PERPAI:-Non trouvée}"
echo "Type de paie (A=acompte, S=régulière) : ${TYPPAI:-Non trouvé}"
echo "Témoin de rappel (0=Inactif, 1=Actif) : ${TEMRAP:-Non trouvé}"
echo "Limite rappel                          : ${LIMRAP:-Non trouvée}"
echo "Rappel général                         : ${RAPGEN:-Non trouvé}"

# Affichage des informations optionnelles si disponibles
echo "Dossiers extraits avant calcul         : ${DOSEXT:-Non trouvé}"
echo "Dossiers en sortie du calcul           : ${DOSSOR:-Non trouvé}"
echo "Nombre nudoss en sortie du calcul      : ${NBNUDOS:-Non trouvé}"

#===============================================================================
# ANALYSE DES DÉPASSEMENTS DE CAPACITÉ
#===============================================================================

echo ""
echo ">>> Analyse des dépassements de capacité"
echo "-----------------------------------------"

# Recherche des dépassements de capacité avec filtrage
if [ "$IGNORE_DEPASSEMENTS" = "1" ]; then
    echo "⚠️  Dépassements de capacité IGNORÉS par configuration"
    nbinfo=0
    nbinfo_total=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)
    echo "   Dépassements détectés mais ignorés : $nbinfo_total"
else
    # Comptage initial
    nbinfo_total=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)

    if [ -n "$IGNORE_CODES" ]; then
        echo "🔍 Filtrage des codes ignorés : $IGNORE_CODES"

        # Créer un fichier temporaire avec les dépassements filtrés
        temp_depassements="$TMP/depassements_filtered_$$.txt"
        grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" > "$temp_depassements"

        # Compter les dépassements après filtrage
        nbinfo=0
        nbinfo_ignores=0

        while read ligne; do
            code_info=$(echo "$ligne" | awk '{print $7}' | cut -c1-4)
            if should_ignore_code "$code_info"; then
                nbinfo_ignores=$((nbinfo_ignores + 1))
                echo "   Code $code_info ignoré"
            else
                nbinfo=$((nbinfo + 1))
            fi
        done < "$temp_depassements"

        rm -f "$temp_depassements"

        echo "   Dépassements total détectés : $nbinfo_total"
        echo "   Dépassements ignorés        : $nbinfo_ignores"
        echo "   Dépassements pris en compte : $nbinfo"
    else
        nbinfo=$nbinfo_total
        echo "   Dépassements détectés : $nbinfo"
    fi

    # Vérification du seuil
    if [ "$nbinfo" -lt "$SEUIL_DEPASSEMENTS" ]; then
        echo "⚠️  Seuil non atteint ($nbinfo < $SEUIL_DEPASSEMENTS) - Dépassements ignorés"
        nbinfo_sous_seuil=$nbinfo
        nbinfo=0
    fi
fi

echo ""
echo ">>> Analyse des messages de débordement"
echo "---------------------------------------"

# Recherche des messages de débordement avec filtrage
if [ "$IGNORE_DEBORDEMENTS" = "1" ]; then
    echo "⚠️  Messages de débordement IGNORÉS par configuration"
    nb_debordement=0
    nb_debordement_total=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)
    echo "   Débordements détectés mais ignorés : $nb_debordement_total"
else
    # Comptage initial
    nb_debordement_total=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)

    if [ -n "$IGNORE_MATRICULES" ]; then
        echo "🔍 Filtrage des matricules ignorés : $IGNORE_MATRICULES"

        # Créer un fichier temporaire avec les débordements filtrés
        temp_debordements="$TMP/debordements_filtered_$$.txt"
        grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_debordements"

        # Compter les débordements après filtrage
        nb_debordement=0
        nb_debordement_ignores=0

        while read ligne; do
            matricule=$(echo "$ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')
            if [ -n "$matricule" ] && should_ignore_matricule "$matricule"; then
                nb_debordement_ignores=$((nb_debordement_ignores + 1))
                echo "   Matricule $matricule ignoré"
            else
                nb_debordement=$((nb_debordement + 1))
            fi
        done < "$temp_debordements"

        rm -f "$temp_debordements"

        echo "   Débordements total détectés : $nb_debordement_total"
        echo "   Débordements ignorés        : $nb_debordement_ignores"
        echo "   Débordements pris en compte : $nb_debordement"
    else
        nb_debordement=$nb_debordement_total
        echo "   Débordements détectés : $nb_debordement"
    fi

    # Vérification du seuil
    if [ "$nb_debordement" -lt "$SEUIL_DEBORDEMENTS" ]; then
        echo "⚠️  Seuil non atteint ($nb_debordement < $SEUIL_DEBORDEMENTS) - Débordements ignorés"
        nb_debordement_sous_seuil=$nb_debordement
        nb_debordement=0
    fi
fi

if [ "$nbinfo" -eq 0 ]; then
    echo "✅ Aucun dépassement de capacité détecté"
    echo "   Le traitement s'est déroulé normalement"
else
    echo "⚠️  ATTENTION: $nbinfo dépassement(s) de capacité détecté(s)"
    if [ "$nb_debordement" -gt 0 ]; then
        echo "⚠️  ATTENTION: $nb_debordement message(s) de débordement d'infos étalées détecté(s)"
    fi
    echo ""

    # Extraction des codes d'information uniques
    info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u)
    echo "Codes d'information concernés: $info"
    echo ""

    # Détail des dépassements par code d'information
    echo ">>> Détail des dépassements par code:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | sort -u | while read Information; do
        nb_occur=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | grep -c "^${Information}$")
        echo "  - Code ${Information} (Période: ${PERPAI:-Inconnue}) - ${nb_occur} occurrence(s)"
    done

    echo ""
    echo ">>> Analyse détaillée des dépassements avec contexte:"

    # Création d'un fichier temporaire pour l'analyse séquentielle
    temp_file="$TMP/depassement_analysis_$$.txt"
    grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_file"

    # Analyse des dépassements avec leur contexte
    ligne_precedente=""
    numero_precedent=0
    depassement_count=0
    debordement_associe_count=0
    debordement_isole_count=0

    while IFS=':' read -r numero_ligne contenu_ligne; do
        if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
            depassement_count=$((depassement_count + 1))

            # Extraction des informations détaillées du dépassement
            code_info=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4)
            table_info=$(echo "$contenu_ligne" | grep -o "(TABLE [^)]*)" | sed 's/[()]//g')

            echo ""
            echo "  🔍 DÉPASSEMENT #$depassement_count (ligne $numero_ligne):"
            echo "     Code d'information : $code_info"
            echo "     ${table_info:-Information table non disponible}"
            echo "     Ligne complète     : $contenu_ligne"

            ligne_precedente="$contenu_ligne"
            numero_precedent="$numero_ligne"

        elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
            # Extraction du matricule du message de débordement
            matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')

            if [ -n "$ligne_precedente" ] && [ $((numero_ligne - numero_precedent)) -le 10 ]; then
                debordement_associe_count=$((debordement_associe_count + 1))
                echo "     ➡️  MESSAGE DE DÉBORDEMENT ASSOCIÉ (ligne $numero_ligne):"
                echo "         Matricule concerné : ${matricule:-Non identifié}"
                echo "         Écart avec dépassement : $((numero_ligne - numero_precedent)) ligne(s)"
                echo "         Message complet : $contenu_ligne"
                echo ""
                echo "     📋 RÉSUMÉ DE L'ASSOCIATION :"
                echo "         Dépassement → Code $code_info"
                echo "         Débordement → Matricule ${matricule:-Non identifié}"
                echo "         Impact      → Données non traitées pour ce matricule"
            else
                debordement_isole_count=$((debordement_isole_count + 1))
                echo ""
                echo "  ⚠️  DÉBORDEMENT ISOLÉ #$debordement_isole_count (ligne $numero_ligne):"
                echo "     Matricule concerné : ${matricule:-Non identifié}"
                echo "     Message complet    : $contenu_ligne"
                echo "     Note : Aucun dépassement associé trouvé dans les 10 lignes précédentes"
            fi

            # Réinitialiser pour éviter les associations multiples
            ligne_precedente=""
            numero_precedent=0
        fi
    done < "$temp_file"

    # Résumé de l'analyse
    echo ""
    echo "  📊 RÉSUMÉ DE L'ANALYSE CONTEXTUELLE :"
    echo "     Dépassements détectés        : $depassement_count"
    echo "     Débordements associés        : $debordement_associe_count"
    echo "     Débordements isolés          : $debordement_isole_count"
    echo "     Total débordements           : $((debordement_associe_count + debordement_isole_count))"

    if [ "$debordement_associe_count" -gt 0 ]; then
        echo "     Taux d'association           : $((debordement_associe_count * 100 / depassement_count))%"
    fi

    # Nettoyage du fichier temporaire
    rm -f "$temp_file"

    echo ""
    echo ">>> Lignes complètes des dépassements:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | while read ligne; do
        echo "  $ligne"
    done

    if [ "$nb_debordement" -gt 0 ]; then
        echo ""
        echo ">>> Tableau récapitulatif des associations:"
        echo "┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐"
        echo "│ Code Info       │ Matricule       │ Ligne Dépass.   │ Ligne Débord.   │"
        echo "├─────────────────┼─────────────────┼─────────────────┼─────────────────┤"

        # Régénération de l'analyse pour le tableau
        temp_table="$TMP/table_analysis_$$.txt"
        grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_table"

        ligne_depass=""
        numero_depass=0
        code_depass=""

        while IFS=':' read -r numero_ligne contenu_ligne; do
            if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
                code_depass=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4)
                ligne_depass="$contenu_ligne"
                numero_depass="$numero_ligne"

            elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
                matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')

                if [ -n "$ligne_depass" ] && [ $((numero_ligne - numero_depass)) -le 10 ]; then
                    # Association trouvée
                    printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" \
                           "${code_depass:-N/A}" \
                           "${matricule:-N/A}" \
                           "$numero_depass" \
                           "$numero_ligne"
                else
                    # Débordement isolé
                    printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" \
                           "ISOLÉ" \
                           "${matricule:-N/A}" \
                           "N/A" \
                           "$numero_ligne"
                fi

                # Réinitialiser
                ligne_depass=""
                numero_depass=0
                code_depass=""
            fi
        done < "$temp_table"

        echo "└─────────────────┴─────────────────┴─────────────────┴─────────────────┘"
        rm -f "$temp_table"

        echo ""
        echo ">>> Liste des matricules concernés:"
        matricules_uniques=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u)
        if [ -n "$matricules_uniques" ]; then
            total_matricules=$(echo "$matricules_uniques" | wc -l)
            echo "  Total de matricules uniques concernés: $total_matricules"
            echo ""
            echo "$matricules_uniques" | while read matricule; do
                nb_occurrences=$(grep -a "MATRICULE $matricule" "${Fich_Log}" | grep -c "NON TRAITE")
                echo "  - $matricule (${nb_occurrences} occurrence(s))"
            done
        fi

        echo ""
        echo ">>> Lignes complètes des messages de débordement:"
        grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | while read ligne; do
            echo "  $ligne"
        done
    fi
fi

#===============================================================================
# ANALYSE COMPLÉMENTAIRE ET STATISTIQUES
#===============================================================================

echo ""
echo ">>> Statistiques du fichier de log"
echo "-----------------------------------"

# Statistiques générales du fichier avec filtrage
nb_lignes=$(wc -l < "${Fich_Log}")

# Gestion des erreurs avec filtrage
if [ "$IGNORE_ERRORS" = "1" ]; then
    echo "⚠️  Erreurs générales IGNORÉES par configuration"
    nb_erreurs=0
    nb_erreurs_total=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Erreurs détectées mais ignorées : $nb_erreurs_total"
else
    nb_erreurs=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Erreurs détectées : $nb_erreurs"
fi

# Gestion des avertissements avec filtrage
if [ "$IGNORE_WARNINGS" = "1" ]; then
    echo "⚠️  Avertissements IGNORÉS par configuration"
    nb_warnings=0
    nb_warnings_total=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Avertissements détectés mais ignorés : $nb_warnings_total"
else
    nb_warnings=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Avertissements détectés : $nb_warnings"
fi



echo "Nombre total de lignes       : $nb_lignes"
echo "Nombre d'erreurs détectées   : $nb_erreurs"
echo "Nombre d'avertissements      : $nb_warnings"
echo "Dépassements de capacité     : $nbinfo"
echo "Messages de débordement      : $nb_debordement"

# Recherche d'autres informations utiles
nb_bbao=$(grep -c "BBAO" "${Fich_Log}" 2>/dev/null || echo "0")
nb_bbad=$(grep -c "BBAD" "${Fich_Log}" 2>/dev/null || echo "0")

echo ""
echo ">>> Codes de traitement détectés"
echo "---------------------------------"
echo "Messages BBAO (informations) : $nb_bbao"
echo "Messages BBAD (données)      : $nb_bbad"

# Recherche des codes d'erreur spécifiques
if [ "$nb_erreurs" -gt 0 ]; then
    echo ""
    echo ">>> Erreurs détectées dans le log:"
    grep -i "error\|erreur\|échec\|failed" "${Fich_Log}" | head -10 | while read ligne_erreur; do
        echo "  $ligne_erreur"
    done
    if [ "$nb_erreurs" -gt 10 ]; then
        echo "  ... et $((nb_erreurs - 10)) autres erreurs"
    fi
fi

#===============================================================================
# RÉSUMÉ ET RECOMMANDATIONS
#===============================================================================

echo ""
echo ">>> Résumé de l'analyse"
echo "========================"

# Détermination du statut global
if [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ] && [ "$nb_erreurs" -eq 0 ]; then
    statut="✅ SUCCÈS"
    recommandation="Le traitement s'est déroulé sans problème."
elif [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    if [ "$nb_erreurs" -eq 0 ]; then
        statut="⚠️  ATTENTION"
        if [ "$nb_debordement" -gt 0 ]; then
            recommandation="Dépassements de capacité avec débordement d'infos étalées détectés. Vérifier la configuration et l'espace disponible."
        else
            recommandation="Dépassements de capacité détectés. Vérifier les paramètres de configuration."
        fi
    else
        statut="❌ ERREUR CRITIQUE"
        recommandation="Dépassements de capacité ET erreurs détectés. Investigation urgente requise."
    fi
elif [ "$nb_erreurs" -gt 0 ]; then
    statut="❌ ERREUR"
    recommandation="Erreurs détectées dans le traitement. Investigation requise."
else
    statut="ℹ️  INFORMATION"
    recommandation="Analyse terminée. Consulter les détails ci-dessus."
fi

echo "Statut              : $statut"
echo "Période traitée     : ${PERPAI:-Inconnue}"
echo "Type de paie        : ${TYPPAI:-Inconnu}"
echo "Dépassements        : $nbinfo"
echo "Messages débordement: $nb_debordement"
echo "Erreurs             : $nb_erreurs"
echo "Recommandation      : $recommandation"

# Résumé des filtres appliqués
echo ""
echo ">>> Résumé des filtres appliqués:"
echo "================================="
filtres_actifs=0

if [ "$IGNORE_DEPASSEMENTS" = "1" ]; then
    echo "🔇 Dépassements de capacité : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_DEBORDEMENTS" = "1" ]; then
    echo "🔇 Messages de débordement : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ -n "$IGNORE_CODES" ]; then
    echo "🔇 Codes ignorés : $IGNORE_CODES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ -n "$IGNORE_MATRICULES" ]; then
    echo "🔇 Matricules ignorés : $IGNORE_MATRICULES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$SEUIL_DEPASSEMENTS" -gt 1 ]; then
    echo "📊 Seuil dépassements : $SEUIL_DEPASSEMENTS (minimum pour alerter)"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$SEUIL_DEBORDEMENTS" -gt 1 ]; then
    echo "📊 Seuil débordements : $SEUIL_DEBORDEMENTS (minimum pour alerter)"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_ERRORS" = "1" ]; then
    echo "🔇 Erreurs générales : IGNORÉES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_WARNINGS" = "1" ]; then
    echo "🔇 Avertissements : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$filtres_actifs" -eq 0 ]; then
    echo "✅ Aucun filtre actif - Analyse complète"
else
    echo ""
    echo "⚠️  $filtres_actifs filtre(s) actif(s) - Certains problèmes peuvent être masqués"
fi

# Recommandations spécifiques basées sur l'analyse
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    echo ""
    echo ">>> Recommandations spécifiques:"
    echo "================================"

    if [ "$nb_debordement" -gt 0 ]; then
        # Extraction des matricules pour recommandations
        matricules_concernes=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u)
        nb_matricules=$(echo "$matricules_concernes" | wc -l)

        echo "🔴 ACTIONS URGENTES :"
        echo "  1. Vérifier le traitement de paie pour $nb_matricules matricule(s)"
        echo "  2. Contrôler les données de paie des matricules suivants :"
        echo "$matricules_concernes" | head -5 | while read mat; do
            echo "     - $mat"
        done
        if [ "$nb_matricules" -gt 5 ]; then
            echo "     ... et $((nb_matricules - 5)) autres matricules (voir liste complète ci-dessus)"
        fi
        echo ""
        echo "🔧 ACTIONS TECHNIQUES :"
        echo "  1. Augmenter l'espace de stockage pour éviter les débordements"
        echo "  2. Optimiser les paramètres de capacité des tables"
        echo "  3. Prévoir un retraitement pour les matricules non traités"
        echo ""
        echo "📋 CONTRÔLES À EFFECTUER :"
        echo "  1. Vérifier la complétude des bulletins de paie"
        echo "  2. Contrôler les montants calculés pour les matricules concernés"
        echo "  3. Valider les déclarations sociales"

    else
        echo "⚠️  SURVEILLANCE :"
        echo "  1. Surveiller les prochains traitements de paie"
        echo "  2. Vérifier la tendance d'évolution des dépassements"
        echo "  3. Prévoir une optimisation préventive"
    fi

    echo ""
    echo "📞 ESCALADE :"
    if [ "$nb_debordement" -gt 0 ]; then
        echo "  Niveau : URGENT - Contacter immédiatement l'équipe technique"
        echo "  Impact : Données de paie potentiellement incomplètes"
    else
        echo "  Niveau : NORMAL - Informer l'équipe technique lors du prochain créneau"
        echo "  Impact : Surveillance préventive recommandée"
    fi
fi

#===============================================================================
# GÉNÉRATION DES RAPPORTS
#===============================================================================

# Calcul du temps d'exécution
DATFIN=$(date +'%H:%M:%S')
FINTRT=$(date +'%s')
DUREE=$((FINTRT - DEBTRT))

# Génération des rapports si demandé
if [ "$GENERATE_PDF" = "1" ] || [ "$GENERATE_XLSX" = "1" ]; then
    echo ""
    echo ">>> Génération des rapports"
    echo "==========================="

    # Création du répertoire de sortie si nécessaire
    if [ ! -d "$REPORT_DIR" ]; then
        mkdir -p "$REPORT_DIR" 2>/dev/null || {
            echo "⚠️  Impossible de créer le répertoire $REPORT_DIR, utilisation de $TMP"
            REPORT_DIR="$TMP"
        }
    fi

    # Nom de base des fichiers avec timestamp
    TIMESTAMP=$(date +'%Y%m%d_%H%M%S')
    BASE_FILENAME="${REPORT_PREFIX}_${TIMESTAMP}"

    echo "Répertoire de sortie : $REPORT_DIR"
    echo "Nom de base         : $BASE_FILENAME"

    # Génération du fichier Excel XLSX
    if [ "$GENERATE_XLSX" = "1" ]; then
        XLSX_FILE="$REPORT_DIR/${BASE_FILENAME}.xlsx"
        echo ""
        echo "📊 Génération du fichier Excel XLSX : $XLSX_FILE"

        # Vérification de la disponibilité de Perl avec Excel::Writer::XLSX
        if command -v perl >/dev/null 2>&1 && perl -e "use Excel::Writer::XLSX" 2>/dev/null; then
            PERL_CMD="perl"
        else
            echo "⚠️  Perl avec Excel::Writer::XLSX non disponible, génération CSV alternative"
            CSV_FILE="$REPORT_DIR/${BASE_FILENAME}.csv"

            # Génération CSV de fallback
            {
                echo "Type,Ligne,Code_Info,Matricule,Message,Timestamp,Fichier_Source"

                if [ "$nbinfo_total" -gt 0 ]; then
                    grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | while IFS=':' read -r numero_ligne contenu_ligne; do
                        code_info=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4 2>/dev/null || echo "N/A")
                        message_clean=$(echo "$contenu_ligne" | sed 's/,/;/g' | sed 's/"//g')
                        echo "DEPASSEMENT,$numero_ligne,$code_info,N/A,\"$message_clean\",$(date +'%Y-%m-%d %H:%M:%S'),$(basename "$Fich_Log")"
                    done
                fi

                if [ "$nb_debordement_total" -gt 0 ]; then
                    grep -n -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | while IFS=':' read -r numero_ligne contenu_ligne; do
                        matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' 2>/dev/null || echo "N/A")
                        message_clean=$(echo "$contenu_ligne" | sed 's/,/;/g' | sed 's/"//g')
                        echo "DEBORDEMENT,$numero_ligne,N/A,$matricule,\"$message_clean\",$(date +'%Y-%m-%d %H:%M:%S'),$(basename "$Fich_Log")"
                    done
                fi

                echo "RESUME,N/A,N/A,N/A,\"Statut: $statut\",$(date +'%Y-%m-%d %H:%M:%S'),$(basename "$Fich_Log")"
                echo "STATISTIQUES,N/A,N/A,N/A,\"Depassements: $nbinfo; Debordements: $nb_debordement; Erreurs: $nb_erreurs\",$(date +'%Y-%m-%d %H:%M:%S'),$(basename "$Fich_Log")"

            } > "$CSV_FILE"

            echo "✅ Fichier CSV généré (fallback) : $CSV_FILE"
            echo "   Pour XLSX, installer : cpan Excel::Writer::XLSX"
            PERL_CMD=""
        fi

        # Génération XLSX avec Perl si disponible
        if [ -n "$PERL_CMD" ]; then
            # Création du script Perl temporaire pour générer XLSX
            PERL_SCRIPT="$TMP/generate_xlsx_$$.pl"

            cat > "$PERL_SCRIPT" << 'EOF'
#!/usr/bin/perl
use strict;
use warnings;
use Excel::Writer::XLSX;
use File::Basename;
use POSIX qw(strftime);

sub create_excel_report {
    my ($xlsx_file, $log_file, $statut, $nbinfo, $nb_debordement, $nb_erreurs,
        $perpai, $typpai, $duree, $ignore_codes, $ignore_matricules, $seuil_depassements) = @_;

    # Création du classeur
    my $workbook = Excel::Writer::XLSX->new($xlsx_file);
    return 0 unless $workbook;

    # Formats de style
    my $header_format = $workbook->add_format(
        bold => 1,
        color => 'white',
        bg_color => '#366092',
        border => 1,
        align => 'center'
    );

    my $title_format = $workbook->add_format(
        bold => 1,
        size => 14
    );

    my $warning_format = $workbook->add_format(
        bold => 1,
        bg_color => '#FFF2CC'
    );

    my $error_format = $workbook->add_format(
        bold => 1,
        bg_color => '#FFCCCB'
    );

    my $success_format = $workbook->add_format(
        bold => 1,
        bg_color => '#D4EDDA'
    );

    my $border_format = $workbook->add_format(border => 1);
    my $bold_format = $workbook->add_format(bold => 1);

    # === FEUILLE 1: RÉSUMÉ EXÉCUTIF ===
    my $ws_resume = $workbook->add_worksheet("Résumé Exécutif");

    # Titre
    $ws_resume->merge_range('A1:D1', "📊 RAPPORT D'ANALYSE DES DÉPASSEMENTS DE CAPACITÉ", $title_format);

    # Informations générales
    my $row = 2;
    $ws_resume->write($row, 0, "Fichier analysé :", $bold_format);
    $ws_resume->write($row, 1, basename($log_file));

    $row++;
    $ws_resume->write($row, 0, "Date d'analyse :", $bold_format);
    $ws_resume->write($row, 1, strftime("%d/%m/%Y à %H:%M:%S", localtime));

    $row++;
    $ws_resume->write($row, 0, "Durée d'analyse :", $bold_format);
    $ws_resume->write($row, 1, "${duree}s");

    $row += 2;
    $ws_resume->write($row, 0, "STATUT GLOBAL", $title_format);

    $row++;
    my $status_format = $border_format;
    if ($statut =~ /SUCCÈS/) {
        $status_format = $success_format;
    } elsif ($statut =~ /ATTENTION/) {
        $status_format = $warning_format;
    } else {
        $status_format = $error_format;
    }
    $ws_resume->write($row, 0, $statut, $status_format);

    # Statistiques principales
    $row += 2;
    $ws_resume->write($row, 0, "STATISTIQUES PRINCIPALES", $title_format);

    $row++;
    my @stats_data = (
        ["Métrique", "Valeur"],
        ["Période de paie", $perpai || "Inconnue"],
        ["Type de paie", $typpai || "Inconnu"],
        ["Dépassements de capacité", $nbinfo],
        ["Messages de débordement", $nb_debordement],
        ["Erreurs générales", $nb_erreurs]
    );

    for my $i (0..$#stats_data) {
        for my $j (0..$#{$stats_data[$i]}) {
            my $format = ($i == 0) ? $header_format : $border_format;
            $ws_resume->write($row + $i, $j, $stats_data[$i][$j], $format);
        }
    }

    # === FEUILLE 2: DÉPASSEMENTS ===
    my $ws_depassements = $workbook->add_worksheet("Dépassements");

    # En-têtes
    my @headers_depass = ("Ligne", "Code Info", "Type Table", "Message Complet", "Timestamp");
    for my $i (0..$#headers_depass) {
        $ws_depassements->write(0, $i, $headers_depass[$i], $header_format);
    }

    # Données des dépassements (placeholder)
    $ws_depassements->write(1, 0, "Aucune donnée de dépassement extraite", $border_format);
    $ws_depassements->write(1, 1, "Voir log original", $border_format);

    # === FEUILLE 3: DÉBORDEMENTS ===
    my $ws_debordements = $workbook->add_worksheet("Débordements");

    # En-têtes
    my @headers_debord = ("Ligne", "Matricule", "Message Complet", "Timestamp");
    for my $i (0..$#headers_debord) {
        $ws_debordements->write(0, $i, $headers_debord[$i], $header_format);
    }

    # Données des débordements (placeholder)
    $ws_debordements->write(1, 0, "Aucune donnée de débordement extraite", $border_format);
    $ws_debordements->write(1, 1, "Voir log original", $border_format);

    # === FEUILLE 4: CONFIGURATION ===
    my $ws_config = $workbook->add_worksheet("Configuration");

    $ws_config->write(0, 0, "CONFIGURATION DES FILTRES", $title_format);

    my @config_data = (
        ["Paramètre", "Valeur"],
        ["Codes ignorés", $ignore_codes || "Aucun"],
        ["Matricules ignorés", $ignore_matricules || "Aucun"],
        ["Seuil dépassements", $seuil_depassements]
    );

    for my $i (0..$#config_data) {
        for my $j (0..$#{$config_data[$i]}) {
            my $format = ($i == 0) ? $header_format : $border_format;
            $ws_config->write($i + 2, $j, $config_data[$i][$j], $format);
        }
    }

    # Ajustement automatique des colonnes
    $ws_resume->set_column('A:A', 25);
    $ws_resume->set_column('B:B', 20);
    $ws_depassements->set_column('A:E', 15);
    $ws_debordements->set_column('A:D', 15);
    $ws_config->set_column('A:B', 20);

    # Fermeture du classeur
    $workbook->close();
    return 1;
}

# Programme principal
if (@ARGV < 9) {
    print "Usage: script.pl xlsx_file log_file statut nbinfo nb_debordement nb_erreurs perpai typpai duree [ignore_codes] [ignore_matricules] [seuil_depassements]\n";
    exit 1;
}

my ($xlsx_file, $log_file, $statut, $nbinfo, $nb_debordement, $nb_erreurs,
    $perpai, $typpai, $duree) = @ARGV[0..8];

my $ignore_codes = $ARGV[9] || "";
my $ignore_matricules = $ARGV[10] || "";
my $seuil_depassements = $ARGV[11] || "1";

eval {
    my $success = create_excel_report($xlsx_file, $log_file, $statut, $nbinfo, $nb_debordement,
                                    $nb_erreurs, $perpai, $typpai, $duree, $ignore_codes,
                                    $ignore_matricules, $seuil_depassements);
    if ($success) {
        print "SUCCESS\n";
    } else {
        print "ERROR\n";
    }
};

if ($@) {
    print "ERROR: $@\n";
    exit 1;
}
EOF

            # Exécution du script Perl
            result=$($PERL_CMD "$PERL_SCRIPT" "$XLSX_FILE" "$Fich_Log" "$statut" "$nbinfo" "$nb_debordement" "$nb_erreurs" "${PERPAI:-Inconnue}" "${TYPPAI:-Inconnu}" "$DUREE" "${IGNORE_CODES:-}" "${IGNORE_MATRICULES:-}" "$SEUIL_DEPASSEMENTS" 2>&1)

            # Nettoyage du script temporaire
            rm -f "$PERL_SCRIPT"

            if [ "$result" = "SUCCESS" ] && [ -f "$XLSX_FILE" ]; then
                echo "✅ Fichier Excel XLSX généré avec succès"
                echo "   Taille : $(wc -c < "$XLSX_FILE") octets"
                echo "   Feuilles : Résumé Exécutif, Dépassements, Débordements, Configuration"
            else
                echo "❌ Erreur lors de la génération du fichier XLSX"
                echo "   Détail : $result"
            fi
        fi
    fi

    # Génération du rapport PDF
    if [ "$GENERATE_PDF" = "1" ]; then
        PDF_FILE="$REPORT_DIR/${BASE_FILENAME}.pdf"
        HTML_FILE="$REPORT_DIR/${BASE_FILENAME}.html"
        echo ""
        echo "📄 Génération du rapport PDF : $PDF_FILE"

        # Génération du fichier HTML intermédiaire
        {
            cat << 'EOF'
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport d'Analyse des Dépassements de Capacité</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 5px; }
        .section h3 { color: #555; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .highlight { background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
EOF

            echo "<div class=\"header\">"
            echo "<h1>📊 Rapport d'Analyse des Dépassements de Capacité</h1>"
            echo "<p><strong>Fichier analysé :</strong> $(basename "$Fich_Log")</p>"
            echo "<p><strong>Date d'analyse :</strong> $(date +'%d/%m/%Y à %H:%M:%S')</p>"
            echo "<p><strong>Durée d'analyse :</strong> ${DUREE}s</p>"
            echo "<p><strong>Environnement :</strong> ${ENVIRONNEMENT:-prod}</p>"
            echo "</div>"

            # Statut global
            echo "<div class=\"section\">"
            echo "<h2>📈 Résumé Exécutif</h2>"
            case "$statut" in
                *"SUCCÈS"*) echo "<p class=\"status-success\">$statut</p>" ;;
                *"ATTENTION"*) echo "<p class=\"status-warning\">$statut</p>" ;;
                *"ERREUR"*) echo "<p class=\"status-error\">$statut</p>" ;;
                *) echo "<p>$statut</p>" ;;
            esac
            echo "<p><strong>Recommandation :</strong> $recommandation</p>"
            echo "</div>"

            # Statistiques
            echo "<div class=\"section\">"
            echo "<h2>📊 Statistiques</h2>"
            echo "<table class=\"table\">"
            echo "<tr><th>Métrique</th><th>Valeur</th></tr>"
            echo "<tr><td>Période de paie</td><td>${PERPAI:-Inconnue}</td></tr>"
            echo "<tr><td>Type de paie</td><td>${TYPPAI:-Inconnu}</td></tr>"
            echo "<tr><td>Dépassements de capacité</td><td>$nbinfo</td></tr>"
            echo "<tr><td>Messages de débordement</td><td>$nb_debordement</td></tr>"
            echo "<tr><td>Erreurs générales</td><td>$nb_erreurs</td></tr>"
            echo "<tr><td>Avertissements</td><td>$nb_warnings</td></tr>"
            echo "<tr><td>Nombre total de lignes</td><td>$nb_lignes</td></tr>"
            echo "</table>"
            echo "</div>"

        } > "$HTML_FILE"

        # Ajout des détails des dépassements si présents
        if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
            {
                echo "<div class=\"section\">"
                echo "<h2>⚠️ Détails des Problèmes</h2>"

                if [ "$nbinfo" -gt 0 ]; then
                    echo "<h3>🔍 Dépassements de Capacité</h3>"
                    echo "<table class=\"table\">"
                    echo "<tr><th>Code Info</th><th>Ligne</th><th>Message</th></tr>"

                    grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | head -10 | while IFS=':' read -r numero_ligne contenu_ligne; do
                        code_info=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4 2>/dev/null || echo "N/A")
                        message_clean=$(echo "$contenu_ligne" | sed 's/</\&lt;/g' | sed 's/>/\&gt;/g')
                        echo "<tr><td>$code_info</td><td>$numero_ligne</td><td class=\"code\">$message_clean</td></tr>"
                    done

                    echo "</table>"
                    if [ "$nbinfo" -gt 10 ]; then
                        echo "<p><em>... et $((nbinfo - 10)) autres dépassements (voir fichier CSV pour la liste complète)</em></p>"
                    fi
                fi

                if [ "$nb_debordement" -gt 0 ]; then
                    echo "<h3>💾 Messages de Débordement</h3>"
                    echo "<table class=\"table\">"
                    echo "<tr><th>Matricule</th><th>Ligne</th><th>Message</th></tr>"

                    grep -n -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | head -10 | while IFS=':' read -r numero_ligne contenu_ligne; do
                        matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' 2>/dev/null || echo "N/A")
                        message_clean=$(echo "$contenu_ligne" | sed 's/</\&lt;/g' | sed 's/>/\&gt;/g')
                        echo "<tr><td>$matricule</td><td>$numero_ligne</td><td class=\"code\">$message_clean</td></tr>"
                    done

                    echo "</table>"
                    if [ "$nb_debordement" -gt 10 ]; then
                        echo "<p><em>... et $((nb_debordement - 10)) autres débordements (voir fichier CSV pour la liste complète)</em></p>"
                    fi
                fi

                echo "</div>"
            } >> "$HTML_FILE"
        fi

        # Ajout des filtres appliqués
        {
            echo "<div class=\"section\">"
            echo "<h2>🔧 Configuration des Filtres</h2>"
            echo "<table class=\"table\">"
            echo "<tr><th>Filtre</th><th>Valeur</th></tr>"
            echo "<tr><td>Ignorer dépassements</td><td>$([ "$IGNORE_DEPASSEMENTS" = "1" ] && echo "OUI" || echo "NON")</td></tr>"
            echo "<tr><td>Ignorer débordements</td><td>$([ "$IGNORE_DEBORDEMENTS" = "1" ] && echo "OUI" || echo "NON")</td></tr>"
            echo "<tr><td>Codes ignorés</td><td>${IGNORE_CODES:-Aucun}</td></tr>"
            echo "<tr><td>Matricules ignorés</td><td>${IGNORE_MATRICULES:-Aucun}</td></tr>"
            echo "<tr><td>Seuil dépassements</td><td>$SEUIL_DEPASSEMENTS</td></tr>"
            echo "<tr><td>Seuil débordements</td><td>$SEUIL_DEBORDEMENTS</td></tr>"
            echo "</table>"
            echo "</div>"

            echo "<div class=\"footer\">"
            echo "<p>Rapport généré automatiquement par Check_depordement.ksh v6.0</p>"
            echo "<p>Équipe TechOps - $(date +'%Y-%m-%d %H:%M:%S')</p>"
            echo "</div>"

            echo "</body></html>"
        } >> "$HTML_FILE"

        # Conversion HTML vers PDF
        if command -v wkhtmltopdf >/dev/null 2>&1; then
            wkhtmltopdf --page-size A4 --margin-top 20mm --margin-bottom 20mm --margin-left 15mm --margin-right 15mm "$HTML_FILE" "$PDF_FILE" >/dev/null 2>&1
            if [ -f "$PDF_FILE" ]; then
                echo "✅ Rapport PDF généré avec succès"
                echo "   Taille : $(wc -c < "$PDF_FILE") octets"
                rm -f "$HTML_FILE"  # Supprimer le fichier HTML temporaire
            else
                echo "❌ Erreur lors de la conversion PDF"
                echo "   Fichier HTML disponible : $HTML_FILE"
            fi
        elif command -v pandoc >/dev/null 2>&1; then
            pandoc "$HTML_FILE" -o "$PDF_FILE" --pdf-engine=wkhtmltopdf 2>/dev/null
            if [ -f "$PDF_FILE" ]; then
                echo "✅ Rapport PDF généré avec succès (via pandoc)"
                echo "   Taille : $(wc -c < "$PDF_FILE") octets"
                rm -f "$HTML_FILE"
            else
                echo "❌ Erreur lors de la conversion PDF via pandoc"
                echo "   Fichier HTML disponible : $HTML_FILE"
            fi
        else
            echo "⚠️  wkhtmltopdf ou pandoc non disponible"
            echo "   Fichier HTML généré : $HTML_FILE"
            echo "   Pour installer wkhtmltopdf : apt-get install wkhtmltopdf (Debian/Ubuntu)"
            echo "   Ou télécharger depuis : https://wkhtmltopdf.org/downloads.html"
        fi
    fi
fi

echo ""
echo "==============================================================================="
echo "Fin de l'analyse des dépassements de capacité"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Heure début     : $DATDEB"
echo "Heure fin       : $DATFIN"
echo "Durée           : ${DUREE}s"
echo "Statut final    : $statut"
echo "==============================================================================="

# Code de retour selon le résultat
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ] || [ "$nb_erreurs" -gt 0 ]; then
    exit 1  # Problème détecté (dépassement, débordement ou erreur)
else
    exit 0  # Tout va bien
fi
