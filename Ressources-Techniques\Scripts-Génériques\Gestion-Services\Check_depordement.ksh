#!/bin/ksh
#-------------------------------------------
# Author      : <PERSON><PERSON><PERSON><PERSON> BENFRAJ
# Date        : 13/04/2021
# Description : Analyse Depassement de capacity pour la paie
# Use Case    : Sous un ${Slice} lancer /admin/TN_exploitation/bin/Analyzer/CheckPaieDepassement.sh $LOGFILE
# Example     : Sous prXX : /admin/TN_exploitation/bin/Analyzer/CheckPaieDepassement.sh FFCALK3W_RJ2.2925
#-------------------------------------------
# Created  : 13/04/2021 by ebenfraj # Hello World
#
#
#
#            DD/MM/YYYY by uam #changes
#-------------------------------------------

Fich_Log=$LOG/$1
cd $TMP
rm -f $TMP/DEPASSEMENT_Paie.csv

S_DATRAIT=`date +'%H_%M_%S'`
[[ ! -z $ZOCODE ]] && DEMANDE=$ZOCODE
[[ ! -z $IDREQU ]] && DEMANDE=$IDREQU
[[ -z $DEMANDE ]] && DEMANDE=$CDPHAS
[[ ! -z $CODSOC ]] && DEMANDE="${DEMANDE} pour ${CODSOC}"
ExecSQL(){
SQL=$1
CONNECT_STRING=$($SIGACS/exploitation/batch/getConnectString.sh)
sqlplus -s $CONNECT_STRING <<EOF
whenever sqlerror exit failure
whenever oserror exit warning
set pagesize 4999
set colsep ";"
set pagesize 2000
set heading ON
set trimspool ON
set headsep off
set feedback off
set lines 2000
set pages 2000
set echo ON
set lines 9999 pages 9999

$SQL;
quit;
EOF
}

NUMERO_TRAVAIL=$(grep -a -m 1 "BBAO0002" ${Fich_Log} | head -1 | awk  '{print $3}')
ZOCODE=$(grep -a -m 1 "BBAO0002" ${Fich_Log} | head -1 | awk  '{print $5}')
nomFich_Log=$(echo ${Fich_Log} | awk -F"/" '{print $NF}')

        TITRE="Lancement de ${DEMANDE} $ZOCODE , fichier log examiné : ${Fich_Log}"
        PERPAI=`grep -a "DAV-00000000-DA18A" ${Fich_Log}|cut -c27-32`
        TYPPAI=`grep -a "DAV-00000000-DA18B" ${Fich_Log}|cut -c37-37`
        LIMRAP=`grep -a "DAV-00000000-DA18B" ${Fich_Log}|cut -c48-51`
        RAPGEN=`grep -a "DAV-00000000-DA18B" ${Fich_Log}|cut -c42-45`
        TEMRAP=`grep -a "DAV-00000000-DA18B" ${Fich_Log}|cut -c39-39`
        #DOSEXT=`grep -a "BMQ-BBAM0021" ${Fich_Log}|cut -c51-59|head -1`
        #DOSSOR=`grep -a "DBA-BBAD0009-EMPLOYEES. (WRITE)" ${Fich_Log}|cut -c46-54`
        #NBNUDOS=`grep -a "DBA-BBAD0009-PAY DOSS.. (WRITE)" ${Fich_Log}|cut -c46-54`
        echo "$TITRE"
        echo "Période de paie : $PERPAI"
        echo "Témoin de Rappel (0: Inactif, 1: Actif) : $TEMRAP"
        echo "Limite rappel : $LIMRAP"
        echo "Rappel général : $RAPGEN"
        echo "Usage de paie (A acompte, S régulière) : $TYPPAI"
        #echo "Dossiers extraits avant calcul de paie : $DOSEXT"
        #echo "Dossiers en sortie du calcul de paie : $DOSSOR"
        #echo "Nombre nudoss en sortie du calcul de paie : $NBNUDOS"
        nbinfo=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" ${Fich_Log} | wc -l)
        info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" ${Fich_Log} | awk '{print $7}' | cut -c1-4 | sort -u)
        if test $nbinfo -ne 0
        then
              echo "Il y a $nbinfo ligne(s) de DEPASSEMENT DE CAPACITE : " $info
              grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" ${Fich_Log}|grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | sort -u |while read Information
                do
                        echo $Information $PERPAI
                done
        fi
