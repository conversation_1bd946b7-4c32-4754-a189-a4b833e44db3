#!/bin/ksh
#===============================================================================
# Script d'analyse des dépassements de capacité pour la paie
#===============================================================================
# Description : Analyse les fichiers de log pour détecter les dépassements de
#               capacité lors des traitements de paie
# Auteur      : Elmokhtar BENFRAJ
# Date        : 13/04/2021
# Version     : 2.0 - Restructuré et amélioré
#
# Paramètres :
#   $1 : Nom du fichier de log à analyser (dans le répertoire $LOG)
#
# Variables d'environnement requises :
#   LOG, TMP, DEMANDE
#
# Variables d'environnement optionnelles :
#   MODE_AFFICHAGE : MINIMAL (défaut, pour client) ou VERBOSE (détails techniques)
#
# Utilisation :
#   ./Check_depordement.ksh <FICHIER_LOG>
#
# Exemples :
#   ./Check_depordement.ksh FFCALK3W_RJ2.2925                    # Mode client (minimal)
#   MODE_AFFICHAGE=VERBOSE ./Check_depordement.ksh PAIE_202312.log  # Mode technique (détaillé)
#   export MODE_AFFICHAGE=VERBOSE; ./Check_depordement.ksh LOG.txt  # Mode technique persistant
#
# Historique :
#   13/04/2021 - ebenfraj - Création initiale
#   $(date +%d/%m/%Y) - TechOps - Restructuration et amélioration
#===============================================================================

#===============================================================================
# CONFIGURATION ET INITIALISATION
#===============================================================================

# Fonction d'aide
show_usage() {
    echo "Usage: $0 <FICHIER_LOG>"
    echo ""
    echo "Paramètres:"
    echo "  FICHIER_LOG  : Nom du fichier de log à analyser (dans \$LOG)"
    echo ""
    echo "Variables d'environnement requises:"
    echo "  LOG    : Répertoire des fichiers de log"
    echo "  TMP    : Répertoire temporaire"
    echo "  DEMANDE: Nom de la demande en cours"
    echo ""
    echo "Variables d'environnement optionnelles:"
    echo "  MODE_AFFICHAGE : MINIMAL (défaut) ou VERBOSE"
    echo "    - MINIMAL : Affichage professionnel pour le client"
    echo "    - VERBOSE : Affichage détaillé pour les équipes techniques"
    echo ""
    echo "Exemples:"
    echo "  $0 FFCALK3W_RJ2.2925                           # Mode client"
    echo "  MODE_AFFICHAGE=VERBOSE $0 PAIE_202312.log      # Mode technique"
    echo "  export MODE_AFFICHAGE=VERBOSE; $0 LOG.txt      # Mode technique persistant"
}

# Validation des paramètres
if [ -z "$1" ]; then
    echo "ERREUR: Nom du fichier de log manquant" >&2
    show_usage
    exit 1
fi

# Validation des variables d'environnement
if [ -z "$LOG" ]; then
    echo "ERREUR: Variable d'environnement LOG non définie" >&2
    exit 1
fi

if [ -z "$TMP" ]; then
    echo "ERREUR: Variable d'environnement TMP non définie" >&2
    exit 1
fi

# Configuration des chemins
Fich_Log="$LOG/$1"
nomFich_Log="$1"

# Validation de l'existence du fichier de log
if [ ! -f "$Fich_Log" ]; then
    echo "ERREUR: Fichier de log introuvable: $Fich_Log" >&2
    exit 1
fi

# Changement vers le répertoire temporaire
cd "$TMP" || {
    echo "ERREUR: Impossible d'accéder au répertoire temporaire: $TMP" >&2
    exit 1
}

# Variables de temps
DATDEB=$(date +'%H:%M:%S')
DEBTRT=$(date +'%s')

# Mode d'affichage : VERBOSE pour les détails techniques, MINIMAL pour le client
MODE_AFFICHAGE=${MODE_AFFICHAGE:-MINIMAL}

# Fonction d'affichage conditionnel
log_verbose() {
    if [ "$MODE_AFFICHAGE" = "VERBOSE" ]; then
        echo "$@"
    fi
}

log_client() {
    echo "$@"
}

# En-tête selon le mode
if [ "$MODE_AFFICHAGE" = "VERBOSE" ]; then
    echo "==============================================================================="
    echo "Analyse des dépassements de capacité - Paie"
    echo "==============================================================================="
    echo "Fichier analysé : $Fich_Log"
    echo "Taille du fichier: $(wc -c < "$Fich_Log") octets"
    echo "Heure début     : $DATDEB"
    echo "==============================================================================="
else
    echo "Analyse du traitement de paie - $(basename "$Fich_Log")"
    echo "Début d'analyse : $DATDEB"
fi

#===============================================================================
# EXTRACTION DES INFORMATIONS DU FICHIER DE LOG
#===============================================================================

log_verbose ">>> Extraction des informations de traitement..."

# Extraction des informations principales
NUMERO_TRAVAIL=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $3}')
ZOCODE=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $5}')

# Extraction des paramètres de paie
PERPAI=$(grep -a "DAV-00000000-DA18A" "${Fich_Log}" | cut -c27-32)
TYPPAI=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c37-37)
LIMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c48-51)
RAPGEN=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c42-45)
TEMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c39-39)

# Informations optionnelles (commentées dans la version originale)
# DOSEXT=$(grep -a "BMQ-BBAM0021" "${Fich_Log}" | cut -c51-59 | head -1)
# DOSSOR=$(grep -a "DBA-BBAD0009-EMPLOYEES. (WRITE)" "${Fich_Log}" | cut -c46-54)
# NBNUDOS=$(grep -a "DBA-BBAD0009-PAY DOSS.. (WRITE)" "${Fich_Log}" | cut -c46-54)

#===============================================================================
# AFFICHAGE DES INFORMATIONS EXTRAITES
#===============================================================================

# Affichage client (minimal)
log_client ""
log_client "Période de paie : ${PERPAI:-Inconnue}"
log_client "Type de paie    : ${TYPPAI:-Inconnu} $([ "$TYPPAI" = "A" ] && echo "(Acompte)" || [ "$TYPPAI" = "S" ] && echo "(Régulière)" || echo "")"

# Affichage détaillé (verbose uniquement)
log_verbose ""
log_verbose ">>> Informations du traitement de paie"
log_verbose "---------------------------------------"
log_verbose "Demande           : ${DEMANDE:-Non définie}"
log_verbose "Code zone         : ${ZOCODE:-Non trouvé}"
log_verbose "Numéro de travail : ${NUMERO_TRAVAIL:-Non trouvé}"
log_verbose "Fichier analysé   : ${nomFich_Log}"
log_verbose ""
log_verbose ">>> Paramètres de paie détaillés"
log_verbose "--------------------------------"
log_verbose "Période de paie                        : ${PERPAI:-Non trouvée}"
log_verbose "Type de paie (A=acompte, S=régulière) : ${TYPPAI:-Non trouvé}"
log_verbose "Témoin de rappel (0=Inactif, 1=Actif) : ${TEMRAP:-Non trouvé}"
log_verbose "Limite rappel                          : ${LIMRAP:-Non trouvée}"
log_verbose "Rappel général                         : ${RAPGEN:-Non trouvé}"

#===============================================================================
# ANALYSE DES DÉPASSEMENTS DE CAPACITÉ
#===============================================================================

# Recherche des dépassements de capacité
nbinfo=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)

# Recherche des messages de débordement associés
nb_debordement=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)

# Affichage client (minimal et professionnel)
log_client ""
if [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ]; then
    log_client "Statut : ✅ TRAITEMENT RÉUSSI"
    log_client "Aucun dépassement de capacité détecté"
else
    log_client "Statut : ⚠️  ATTENTION REQUISE"
    if [ "$nbinfo" -gt 0 ]; then
        log_client "Dépassements de capacité : $nbinfo"
    fi
    if [ "$nb_debordement" -gt 0 ]; then
        log_client "Messages de débordement  : $nb_debordement"
    fi

    # Extraction des codes d'information pour le client
    if [ "$nbinfo" -gt 0 ]; then
        info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u)
        log_client "Codes concernés          : $info"
    fi
fi

# Affichage détaillé (verbose uniquement)
log_verbose ""
log_verbose ">>> Analyse des dépassements de capacité"
log_verbose "-----------------------------------------"

if [ "$nbinfo" -eq 0 ]; then
    log_verbose "✅ Aucun dépassement de capacité détecté"
    log_verbose "   Le traitement s'est déroulé normalement"
else
    log_verbose "⚠️  ATTENTION: $nbinfo dépassement(s) de capacité détecté(s)"
    if [ "$nb_debordement" -gt 0 ]; then
        log_verbose "⚠️  ATTENTION: $nb_debordement message(s) de débordement d'infos étalées détecté(s)"
    fi
    log_verbose ""

    # Extraction des codes d'information uniques
    info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u)
    log_verbose "Codes d'information concernés: $info"
    log_verbose ""

    # Détail des dépassements par code d'information (verbose uniquement)
    log_verbose ">>> Détail des dépassements par code:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | sort -u | while read Information; do
        nb_occur=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | grep -c "^${Information}$")
        log_verbose "  - Code ${Information} (Période: ${PERPAI:-Inconnue}) - ${nb_occur} occurrence(s)"
    done

    log_verbose ""
    log_verbose ">>> Analyse détaillée des dépassements avec contexte:"

    # Analyse contextuelle détaillée (verbose uniquement)
    if [ "$MODE_AFFICHAGE" = "VERBOSE" ]; then
        # Création d'un fichier temporaire pour l'analyse séquentielle
        temp_file="$TMP/depassement_analysis_$$.txt"
        grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_file"

        # Analyse des dépassements avec leur contexte
        ligne_precedente=""
        numero_precedent=0

        while IFS=':' read -r numero_ligne contenu_ligne; do
            if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
                log_verbose ""
                log_verbose "  🔍 Dépassement détecté à la ligne $numero_ligne:"
                log_verbose "     $contenu_ligne"
                ligne_precedente="$contenu_ligne"
                numero_precedent="$numero_ligne"
            elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
                if [ -n "$ligne_precedente" ] && [ $((numero_ligne - numero_precedent)) -le 5 ]; then
                    log_verbose "  ➡️  Message de débordement associé (ligne $numero_ligne):"
                    log_verbose "     $contenu_ligne"
                    log_verbose "     📊 Écart: $((numero_ligne - numero_precedent)) ligne(s)"
                else
                    log_verbose ""
                    log_verbose "  ⚠️  Message de débordement isolé à la ligne $numero_ligne:"
                    log_verbose "     $contenu_ligne"
                fi
                ligne_precedente=""
                numero_precedent=0
            fi
        done < "$temp_file"

        # Nettoyage du fichier temporaire
        rm -f "$temp_file"

        log_verbose ""
        log_verbose ">>> Lignes complètes des dépassements:"
        grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | while read ligne; do
            log_verbose "  $ligne"
        done

        if [ "$nb_debordement" -gt 0 ]; then
            log_verbose ""
            log_verbose ">>> Lignes complètes des messages de débordement:"
            grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | while read ligne; do
                log_verbose "  $ligne"
            done
        fi
    fi
fi

#===============================================================================
# ANALYSE COMPLÉMENTAIRE ET STATISTIQUES
#===============================================================================

# Statistiques générales du fichier (calcul pour tous les modes)
nb_lignes=$(wc -l < "${Fich_Log}")
nb_erreurs=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
nb_warnings=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")

# Affichage détaillé des statistiques (verbose uniquement)
log_verbose ""
log_verbose ">>> Statistiques du fichier de log"
log_verbose "-----------------------------------"
log_verbose "Nombre total de lignes       : $nb_lignes"
log_verbose "Nombre d'erreurs détectées   : $nb_erreurs"
log_verbose "Nombre d'avertissements      : $nb_warnings"
log_verbose "Dépassements de capacité     : $nbinfo"
log_verbose "Messages de débordement      : $nb_debordement"

# Recherche d'autres informations utiles (verbose uniquement)
if [ "$MODE_AFFICHAGE" = "VERBOSE" ]; then
    nb_bbao=$(grep -c "BBAO" "${Fich_Log}" 2>/dev/null || echo "0")
    nb_bbad=$(grep -c "BBAD" "${Fich_Log}" 2>/dev/null || echo "0")

    log_verbose ""
    log_verbose ">>> Codes de traitement détectés"
    log_verbose "---------------------------------"
    log_verbose "Messages BBAO (informations) : $nb_bbao"
    log_verbose "Messages BBAD (données)      : $nb_bbad"

    # Recherche des codes d'erreur spécifiques
    if [ "$nb_erreurs" -gt 0 ]; then
        log_verbose ""
        log_verbose ">>> Erreurs détectées dans le log:"
        grep -i "error\|erreur\|échec\|failed" "${Fich_Log}" | head -10 | while read ligne_erreur; do
            log_verbose "  $ligne_erreur"
        done
        if [ "$nb_erreurs" -gt 10 ]; then
            log_verbose "  ... et $((nb_erreurs - 10)) autres erreurs"
        fi
    fi
fi

#===============================================================================
# RÉSUMÉ ET RECOMMANDATIONS
#===============================================================================

# Détermination du statut global
if [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ] && [ "$nb_erreurs" -eq 0 ]; then
    statut="✅ SUCCÈS"
    statut_client="✅ TRAITEMENT RÉUSSI"
    recommandation="Le traitement s'est déroulé sans problème."
    recommandation_client="Aucune action requise."
elif [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    if [ "$nb_erreurs" -eq 0 ]; then
        statut="⚠️  ATTENTION"
        statut_client="⚠️  ATTENTION REQUISE"
        if [ "$nb_debordement" -gt 0 ]; then
            recommandation="Dépassements de capacité avec débordement d'infos étalées détectés. Vérifier la configuration et l'espace disponible."
            recommandation_client="Vérifier la configuration et l'espace disponible."
        else
            recommandation="Dépassements de capacité détectés. Vérifier les paramètres de configuration."
            recommandation_client="Vérifier les paramètres de configuration."
        fi
    else
        statut="❌ ERREUR CRITIQUE"
        statut_client="❌ ERREUR CRITIQUE"
        recommandation="Dépassements de capacité ET erreurs détectés. Investigation urgente requise."
        recommandation_client="Investigation urgente requise."
    fi
elif [ "$nb_erreurs" -gt 0 ]; then
    statut="❌ ERREUR"
    statut_client="❌ ERREUR"
    recommandation="Erreurs détectées dans le traitement. Investigation requise."
    recommandation_client="Investigation requise."
else
    statut="ℹ️  INFORMATION"
    statut_client="ℹ️  INFORMATION"
    recommandation="Analyse terminée. Consulter les détails ci-dessus."
    recommandation_client="Analyse terminée."
fi

# Affichage client (minimal et professionnel)
log_client ""
log_client "Recommandation : $recommandation_client"

# Affichage détaillé (verbose uniquement)
log_verbose ""
log_verbose ">>> Résumé de l'analyse"
log_verbose "========================"
log_verbose "Statut              : $statut"
log_verbose "Période traitée     : ${PERPAI:-Inconnue}"
log_verbose "Type de paie        : ${TYPPAI:-Inconnu}"
log_verbose "Dépassements        : $nbinfo"
log_verbose "Messages débordement: $nb_debordement"
log_verbose "Erreurs             : $nb_erreurs"
log_verbose "Recommandation      : $recommandation"

#===============================================================================
# FIN DU TRAITEMENT
#===============================================================================

# Calcul du temps d'exécution
DATFIN=$(date +'%H:%M:%S')
FINTRT=$(date +'%s')
DUREE=$((FINTRT - DEBTRT))

# Affichage client (minimal et professionnel)
log_client ""
log_client "Fin d'analyse : $DATFIN (Durée: ${DUREE}s)"

# Affichage détaillé (verbose uniquement)
log_verbose ""
log_verbose "==============================================================================="
log_verbose "Fin de l'analyse des dépassements de capacité"
log_verbose "==============================================================================="
log_verbose "Fichier analysé : $Fich_Log"
log_verbose "Heure début     : $DATDEB"
log_verbose "Heure fin       : $DATFIN"
log_verbose "Durée           : ${DUREE}s"
log_verbose "Statut final    : $statut"
log_verbose "==============================================================================="

# Code de retour selon le résultat
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ] || [ "$nb_erreurs" -gt 0 ]; then
    exit 1  # Problème détecté (dépassement, débordement ou erreur)
else
    exit 0  # Tout va bien
fi
