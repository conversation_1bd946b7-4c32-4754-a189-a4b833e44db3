#!/bin/ksh
#===============================================================================
# Script d'analyse des dépassements de capacité pour la paie
#===============================================================================
# Description : Analyse les fichiers de log pour détecter les dépassements de
#               capacité lors des traitements de paie
# Auteur      : Elmokhtar BENFRAJ
# Date        : 13/04/2021
# Version     : 2.0 - Restructuré et amélioré
#
# Paramètres :
#   $1 : Nom du fichier de log à analyser (dans le répertoire $LOG)
#
# Variables d'environnement requises :
#   LOG, TMP, DEMANDE
#
# Utilisation :
#   ./Check_depordement.ksh <FICHIER_LOG>
#
# Exemples :
#   ./Check_depordement.ksh FFCALK3W_RJ2.2925
#   ./Check_depordement.ksh PAIE_202312.log
#
# Historique :
#   13/04/2021 - ebenfraj - Création initiale
#   $(date +%d/%m/%Y) - TechOps - Restructuration et amélioration
#===============================================================================

#===============================================================================
# CONFIGURATION ET INITIALISATION
#===============================================================================

# Fonction d'aide
show_usage() {
    echo "Usage: $0 <FICHIER_LOG>"
    echo ""
    echo "Paramètres:"
    echo "  FICHIER_LOG  : Nom du fichier de log à analyser (dans \$LOG)"
    echo ""
    echo "Variables d'environnement requises:"
    echo "  LOG    : Répertoire des fichiers de log"
    echo "  TMP    : Répertoire temporaire"
    echo "  DEMANDE: Nom de la demande en cours"
    echo ""
    echo "Exemples:"
    echo "  $0 FFCALK3W_RJ2.2925"
    echo "  $0 PAIE_202312.log"
}

# Validation des paramètres
if [ -z "$1" ]; then
    echo "ERREUR: Nom du fichier de log manquant" >&2
    show_usage
    exit 1
fi

# Validation des variables d'environnement
if [ -z "$LOG" ]; then
    echo "ERREUR: Variable d'environnement LOG non définie" >&2
    exit 1
fi

if [ -z "$TMP" ]; then
    echo "ERREUR: Variable d'environnement TMP non définie" >&2
    exit 1
fi

# Configuration des chemins
Fich_Log="$LOG/$1"
nomFich_Log="$1"

# Validation de l'existence du fichier de log
if [ ! -f "$Fich_Log" ]; then
    echo "ERREUR: Fichier de log introuvable: $Fich_Log" >&2
    exit 1
fi

# Changement vers le répertoire temporaire
cd "$TMP" || {
    echo "ERREUR: Impossible d'accéder au répertoire temporaire: $TMP" >&2
    exit 1
}

# Variables de temps
DATDEB=$(date +'%H:%M:%S')
DEBTRT=$(date +'%s')

echo "==============================================================================="
echo "Analyse des dépassements de capacité - Paie"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Taille du fichier: $(wc -c < "$Fich_Log") octets"
echo "Heure début     : $DATDEB"
echo "==============================================================================="

#===============================================================================
# EXTRACTION DES INFORMATIONS DU FICHIER DE LOG
#===============================================================================

echo ">>> Extraction des informations de traitement..."

# Extraction des informations principales
NUMERO_TRAVAIL=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $3}')
ZOCODE=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $5}')

# Extraction des paramètres de paie
PERPAI=$(grep -a "DAV-00000000-DA18A" "${Fich_Log}" | cut -c27-32)
TYPPAI=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c37-37)
LIMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c48-51)
RAPGEN=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c42-45)
TEMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c39-39)

# Informations optionnelles (commentées dans la version originale)
# DOSEXT=$(grep -a "BMQ-BBAM0021" "${Fich_Log}" | cut -c51-59 | head -1)
# DOSSOR=$(grep -a "DBA-BBAD0009-EMPLOYEES. (WRITE)" "${Fich_Log}" | cut -c46-54)
# NBNUDOS=$(grep -a "DBA-BBAD0009-PAY DOSS.. (WRITE)" "${Fich_Log}" | cut -c46-54)

#===============================================================================
# AFFICHAGE DES INFORMATIONS EXTRAITES
#===============================================================================

echo ""
echo ">>> Informations du traitement de paie"
echo "---------------------------------------"
echo "Demande           : ${DEMANDE:-Non définie}"
echo "Code zone         : ${ZOCODE:-Non trouvé}"
echo "Numéro de travail : ${NUMERO_TRAVAIL:-Non trouvé}"
echo "Fichier analysé   : ${nomFich_Log}"
echo ""
echo ">>> Paramètres de paie"
echo "----------------------"
echo "Période de paie                        : ${PERPAI:-Non trouvée}"
echo "Type de paie (A=acompte, S=régulière) : ${TYPPAI:-Non trouvé}"
echo "Témoin de rappel (0=Inactif, 1=Actif) : ${TEMRAP:-Non trouvé}"
echo "Limite rappel                          : ${LIMRAP:-Non trouvée}"
echo "Rappel général                         : ${RAPGEN:-Non trouvé}"

# Affichage des informations optionnelles si disponibles
# echo "Dossiers extraits avant calcul         : ${DOSEXT:-Non trouvé}"
# echo "Dossiers en sortie du calcul           : ${DOSSOR:-Non trouvé}"
# echo "Nombre nudoss en sortie du calcul      : ${NBNUDOS:-Non trouvé}"

#===============================================================================
# ANALYSE DES DÉPASSEMENTS DE CAPACITÉ
#===============================================================================

echo ""
echo ">>> Analyse des dépassements de capacité"
echo "-----------------------------------------"

# Recherche des dépassements de capacité
nbinfo=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)

# Recherche des messages de débordement associés
nb_debordement=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)

if [ "$nbinfo" -eq 0 ]; then
    echo "✅ Aucun dépassement de capacité détecté"
    echo "   Le traitement s'est déroulé normalement"
else
    echo "⚠️  ATTENTION: $nbinfo dépassement(s) de capacité détecté(s)"
    if [ "$nb_debordement" -gt 0 ]; then
        echo "⚠️  ATTENTION: $nb_debordement message(s) de débordement d'infos étalées détecté(s)"
    fi
    echo ""

    # Extraction des codes d'information uniques
    info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u)
    echo "Codes d'information concernés: $info"
    echo ""

    # Détail des dépassements par code d'information
    echo ">>> Détail des dépassements par code:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | sort -u | while read Information; do
        nb_occur=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | grep -c "^${Information}$")
        echo "  - Code ${Information} (Période: ${PERPAI:-Inconnue}) - ${nb_occur} occurrence(s)"
    done

    echo ""
    echo ">>> Analyse détaillée des dépassements avec contexte:"

    # Création d'un fichier temporaire pour l'analyse séquentielle
    temp_file="$TMP/depassement_analysis_$$.txt"
    grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_file"

    # Analyse des dépassements avec leur contexte
    ligne_precedente=""
    numero_precedent=0

    while IFS=':' read -r numero_ligne contenu_ligne; do
        if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
            echo ""
            echo "  🔍 Dépassement détecté à la ligne $numero_ligne:"
            echo "     $contenu_ligne"
            ligne_precedente="$contenu_ligne"
            numero_precedent="$numero_ligne"
        elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
            if [ -n "$ligne_precedente" ] && [ $((numero_ligne - numero_precedent)) -le 5 ]; then
                echo "  ➡️  Message de débordement associé (ligne $numero_ligne):"
                echo "     $contenu_ligne"
                echo "     📊 Écart: $((numero_ligne - numero_precedent)) ligne(s)"
            else
                echo ""
                echo "  ⚠️  Message de débordement isolé à la ligne $numero_ligne:"
                echo "     $contenu_ligne"
            fi
            ligne_precedente=""
            numero_precedent=0
        fi
    done < "$temp_file"

    # Nettoyage du fichier temporaire
    rm -f "$temp_file"

    echo ""
    echo ">>> Lignes complètes des dépassements:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | while read ligne; do
        echo "  $ligne"
    done

    if [ "$nb_debordement" -gt 0 ]; then
        echo ""
        echo ">>> Lignes complètes des messages de débordement:"
        grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | while read ligne; do
            echo "  $ligne"
        done
    fi
fi

#===============================================================================
# ANALYSE COMPLÉMENTAIRE ET STATISTIQUES
#===============================================================================

echo ""
echo ">>> Statistiques du fichier de log"
echo "-----------------------------------"

# Statistiques générales du fichier
nb_lignes=$(wc -l < "${Fich_Log}")
nb_erreurs=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
nb_warnings=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")

echo "Nombre total de lignes       : $nb_lignes"
echo "Nombre d'erreurs détectées   : $nb_erreurs"
echo "Nombre d'avertissements      : $nb_warnings"
echo "Dépassements de capacité     : $nbinfo"
echo "Messages de débordement      : $nb_debordement"

# Recherche d'autres informations utiles
nb_bbao=$(grep -c "BBAO" "${Fich_Log}" 2>/dev/null || echo "0")
nb_bbad=$(grep -c "BBAD" "${Fich_Log}" 2>/dev/null || echo "0")

echo ""
echo ">>> Codes de traitement détectés"
echo "---------------------------------"
echo "Messages BBAO (informations) : $nb_bbao"
echo "Messages BBAD (données)      : $nb_bbad"

# Recherche des codes d'erreur spécifiques
if [ "$nb_erreurs" -gt 0 ]; then
    echo ""
    echo ">>> Erreurs détectées dans le log:"
    grep -i "error\|erreur\|échec\|failed" "${Fich_Log}" | head -10 | while read ligne_erreur; do
        echo "  $ligne_erreur"
    done
    if [ "$nb_erreurs" -gt 10 ]; then
        echo "  ... et $((nb_erreurs - 10)) autres erreurs"
    fi
fi

#===============================================================================
# RÉSUMÉ ET RECOMMANDATIONS
#===============================================================================

echo ""
echo ">>> Résumé de l'analyse"
echo "========================"

# Détermination du statut global
if [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ] && [ "$nb_erreurs" -eq 0 ]; then
    statut="✅ SUCCÈS"
    recommandation="Le traitement s'est déroulé sans problème."
elif [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    if [ "$nb_erreurs" -eq 0 ]; then
        statut="⚠️  ATTENTION"
        if [ "$nb_debordement" -gt 0 ]; then
            recommandation="Dépassements de capacité avec débordement d'infos étalées détectés. Vérifier la configuration et l'espace disponible."
        else
            recommandation="Dépassements de capacité détectés. Vérifier les paramètres de configuration."
        fi
    else
        statut="❌ ERREUR CRITIQUE"
        recommandation="Dépassements de capacité ET erreurs détectés. Investigation urgente requise."
    fi
elif [ "$nb_erreurs" -gt 0 ]; then
    statut="❌ ERREUR"
    recommandation="Erreurs détectées dans le traitement. Investigation requise."
else
    statut="ℹ️  INFORMATION"
    recommandation="Analyse terminée. Consulter les détails ci-dessus."
fi

echo "Statut              : $statut"
echo "Période traitée     : ${PERPAI:-Inconnue}"
echo "Type de paie        : ${TYPPAI:-Inconnu}"
echo "Dépassements        : $nbinfo"
echo "Messages débordement: $nb_debordement"
echo "Erreurs             : $nb_erreurs"
echo "Recommandation      : $recommandation"

#===============================================================================
# FIN DU TRAITEMENT
#===============================================================================

# Calcul du temps d'exécution
DATFIN=$(date +'%H:%M:%S')
FINTRT=$(date +'%s')
DUREE=$((FINTRT - DEBTRT))

echo ""
echo "==============================================================================="
echo "Fin de l'analyse des dépassements de capacité"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Heure début     : $DATDEB"
echo "Heure fin       : $DATFIN"
echo "Durée           : ${DUREE}s"
echo "Statut final    : $statut"
echo "==============================================================================="

# Code de retour selon le résultat
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ] || [ "$nb_erreurs" -gt 0 ]; then
    exit 1  # Problème détecté (dépassement, débordement ou erreur)
else
    exit 0  # Tout va bien
fi
