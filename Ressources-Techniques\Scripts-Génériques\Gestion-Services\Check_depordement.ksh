#!/bin/ksh
#===============================================================================
# Script d'analyse des dépassements de capacité pour la paie
#===============================================================================
# Description : Analyse les fichiers de log pour détecter les dépassements de
#               capacité lors des traitements de paie
# Auteur      : Elmokhtar BENFRAJ
# Date        : 13/04/2021
# Version     : 2.0 - Restructuré et amélioré
#
# Paramètres :
#   $1 : Nom du fichier de log à analyser (dans le répertoire $LOG)
#
# Variables d'environnement requises :
#   LOG, TMP, DEMANDE
#
# Utilisation :
#   ./Check_depordement.ksh <FICHIER_LOG>
#
# Variables d'environnement optionnelles pour filtrage :
#   IGNORE_DEPASSEMENTS : 1 pour ignorer les dépassements de capacité (défaut: 0)
#   IGNORE_DEBORDEMENTS : 1 pour ignorer les messages de débordement (défaut: 0)
#   IGNORE_CODES : Liste des codes d'information à ignorer (séparés par des virgules)
#   IGNORE_MATRICULES : Liste des matricules à ignorer (séparés par des virgules)
#   SEUIL_DEPASSEMENTS : Nombre minimum de dépassements pour déclencher une alerte (défaut: 1)
#   SEUIL_DEBORDEMENTS : Nombre minimum de débordements pour déclencher une alerte (défaut: 1)
#   IGNORE_ERRORS : 1 pour ignorer les erreurs générales (défaut: 1)
#   IGNORE_WARNINGS : 1 pour ignorer les avertissements (défaut: 0)
#
# Variables d'envoi d'email :
#   EMAIL_DEST : Adresses email pour envoi automatique (séparées par des virgules)
#   EMAIL_SUBJECT : Sujet de l'email (défaut: "Rapport d'analyse de paie")
#   SEND_EMAIL : 1 pour envoyer un email automatiquement (défaut: 0)
#
# Exemples :
#   ./Check_depordement.ksh FFCALK3W_RJ2.2925
#   ./Check_depordement.ksh PAIE_202312.log
#   export IGNORE_CODES="ZYWA,TEST"; ./Check_depordement.ksh LOG.txt
#   export SEUIL_DEPASSEMENTS=3; ./Check_depordement.ksh LOG.txt
#   export EMAIL_DEST="<EMAIL>"; ./Check_depordement.ksh LOG.txt
#
# Historique :
#   13/04/2021 - ebenfraj - Création initiale
#   $(date +%d/%m/%Y) - TechOps - Restructuration et amélioration
#===============================================================================

#===============================================================================
# CONFIGURATION ET INITIALISATION
#===============================================================================

# Fonction d'aide
show_usage() {
    echo "Usage: $0 <FICHIER_LOG>"
    echo ""
    echo "Paramètres:"
    echo "  FICHIER_LOG  : Nom du fichier de log à analyser (dans \$LOG)"
    echo ""
    echo "Variables d'environnement requises:"
    echo "  LOG    : Répertoire des fichiers de log"
    echo "  TMP    : Répertoire temporaire"
    echo "  DEMANDE: Nom de la demande en cours"
    echo ""
    echo "Exemples:"
    echo "  $0 FFCALK3W_RJ2.2925"
    echo "  $0 PAIE_202312.log"
}

# Validation des paramètres
if [ -z "$1" ]; then
    echo "ERREUR: Nom du fichier de log manquant" >&2
    show_usage
    exit 1
fi

# Validation des variables d'environnement
if [ -z "$LOG" ]; then
    echo "ERREUR: Variable d'environnement LOG non définie" >&2
    exit 1
fi

if [ -z "$TMP" ]; then
    echo "ERREUR: Variable d'environnement TMP non définie" >&2
    exit 1
fi

# Configuration des chemins
Fich_Log="$LOG/$1"
nomFich_Log="$1"

# Validation de l'existence du fichier de log
if [ ! -f "$Fich_Log" ]; then
    echo "ERREUR: Fichier de log introuvable: $Fich_Log" >&2
    exit 1
fi

# Changement vers le répertoire temporaire
cd "$TMP" || {
    echo "ERREUR: Impossible d'accéder au répertoire temporaire: $TMP" >&2
    exit 1
}

# Variables de temps
DATDEB=$(date +'%H:%M:%S')
DEBTRT=$(date +'%s')

#===============================================================================
# CONFIGURATION DES FILTRES D'ERREURS
#===============================================================================

# Variables de filtrage avec valeurs par défaut
IGNORE_DEPASSEMENTS=${IGNORE_DEPASSEMENTS:-0}
IGNORE_DEBORDEMENTS=${IGNORE_DEBORDEMENTS:-0}
IGNORE_CODES=${IGNORE_CODES:-""}
IGNORE_MATRICULES=${IGNORE_MATRICULES:-""}
SEUIL_DEPASSEMENTS=${SEUIL_DEPASSEMENTS:-1}
SEUIL_DEBORDEMENTS=${SEUIL_DEBORDEMENTS:-1}
IGNORE_ERRORS=${IGNORE_ERRORS:-1}
IGNORE_WARNINGS=${IGNORE_WARNINGS:-0}

# Variables d'envoi d'email
EMAIL_DEST=${EMAIL_DEST:-""}
EMAIL_SUBJECT=${EMAIL_SUBJECT:-"Rapport d'analyse de paie"}
SEND_EMAIL=${SEND_EMAIL:-0}

# Variables de logging
SCRIPT_LOG_FILE="$LOG/Check_depordement_$(date +'%Y%m%d').log"
EMAIL_LOG_FILE="$LOG/Check_depordement_emails_$(date +'%Y%m%d').log"

# Fonction de logging
log_message() {
    local message="$1"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$SCRIPT_LOG_FILE"
}

# Fonction de logging email
log_email() {
    local action="$1"
    local details="$2"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $action - $details" >> "$EMAIL_LOG_FILE"
}

# Fonction pour vérifier si un code doit être ignoré
should_ignore_code() {
    local code="$1"
    if [ -n "$IGNORE_CODES" ]; then
        echo "$IGNORE_CODES" | grep -q "\b$code\b"
        return $?
    fi
    return 1
}

# Fonction pour vérifier si un matricule doit être ignoré
should_ignore_matricule() {
    local matricule="$1"
    if [ -n "$IGNORE_MATRICULES" ]; then
        echo "$IGNORE_MATRICULES" | grep -q "\b$matricule\b"
        return $?
    fi
    return 1
}

echo "==============================================================================="
echo "Analyse des dépassements de capacité - Paie"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Taille du fichier: $(wc -c < "$Fich_Log") octets"
echo "Heure début     : $DATDEB"
echo ""
echo "Configuration des filtres :"
echo "  Ignorer dépassements     : $([ "$IGNORE_DEPASSEMENTS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Ignorer débordements     : $([ "$IGNORE_DEBORDEMENTS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Seuil dépassements       : $SEUIL_DEPASSEMENTS"
echo "  Seuil débordements       : $SEUIL_DEBORDEMENTS"
echo "  Codes ignorés            : ${IGNORE_CODES:-Aucun}"
echo "  Matricules ignorés       : ${IGNORE_MATRICULES:-Aucun}"
echo "  Ignorer erreurs générales: $([ "$IGNORE_ERRORS" = "1" ] && echo "OUI" || echo "NON")"
echo "  Ignorer avertissements   : $([ "$IGNORE_WARNINGS" = "1" ] && echo "OUI" || echo "NON")"
echo ""
echo "Configuration email :"
echo "  Envoi automatique        : $([ "$SEND_EMAIL" = "1" ] || [ -n "$EMAIL_DEST" ] && echo "OUI" || echo "NON")"
echo "  Destinataires            : ${EMAIL_DEST:-Aucun}"
echo "  Sujet                    : $EMAIL_SUBJECT"
echo "==============================================================================="

# Initialisation du logging
log_message "=== DÉBUT D'ANALYSE ==="
log_message "Fichier analysé: $Fich_Log"
log_message "Utilisateur: $(whoami)"
log_message "Répertoire: $(pwd)"
log_message "Commande: $0 $*"
log_message "Configuration - EMAIL_DEST: ${EMAIL_DEST:-Aucun}"
log_message "Configuration - SEUIL_DEPASSEMENTS: $SEUIL_DEPASSEMENTS"
log_message "Configuration - SEUIL_DEBORDEMENTS: $SEUIL_DEBORDEMENTS"

#===============================================================================
# EXTRACTION DES INFORMATIONS DU FICHIER DE LOG
#===============================================================================

echo ">>> Extraction des informations de traitement..."

# Extraction des informations principales
NUMERO_TRAVAIL=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $3}')
ZOCODE=$(grep -a -m 1 "BBAO0002" "${Fich_Log}" | head -1 | awk '{print $5}')

# Extraction des paramètres de paie
PERPAI=$(grep -a "DAV-00000000-DA18A" "${Fich_Log}" | cut -c27-32)
TYPPAI=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c37-37)
LIMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c48-51)
RAPGEN=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c42-45)
TEMRAP=$(grep -a "DAV-00000000-DA18B" "${Fich_Log}" | cut -c39-39)

# Informations optionnelles (commentées dans la version originale)
DOSEXT=$(grep -a "BMQ-BBAM0021" "${Fich_Log}" | cut -c51-59 | head -1)
DOSSOR=$(grep -a "DBA-BBAD0009-EMPLOYEES. (WRITE)" "${Fich_Log}" | cut -c46-54)
NBNUDOS=$(grep -a "DBA-BBAD0009-PAY DOSS.. (WRITE)" "${Fich_Log}" | cut -c46-54)

#===============================================================================
# AFFICHAGE DES INFORMATIONS EXTRAITES
#===============================================================================

echo ""
echo ">>> Informations du traitement de paie"
echo "---------------------------------------"
echo "Demande           : ${DEMANDE:-Non définie}"
echo "Code zone         : ${ZOCODE:-Non trouvé}"
echo "Numéro de travail : ${NUMERO_TRAVAIL:-Non trouvé}"
echo "Fichier analysé   : ${nomFich_Log}"
echo ""
echo ">>> Paramètres de paie"
echo "----------------------"
echo "Période de paie                        : ${PERPAI:-Non trouvée}"
echo "Type de paie (A=acompte, S=régulière) : ${TYPPAI:-Non trouvé}"
echo "Témoin de rappel (0=Inactif, 1=Actif) : ${TEMRAP:-Non trouvé}"
echo "Limite rappel                          : ${LIMRAP:-Non trouvée}"
echo "Rappel général                         : ${RAPGEN:-Non trouvé}"

# Affichage des informations optionnelles si disponibles
echo "Dossiers extraits avant calcul         : ${DOSEXT:-Non trouvé}"
echo "Dossiers en sortie du calcul           : ${DOSSOR:-Non trouvé}"
echo "Nombre nudoss en sortie du calcul      : ${NBNUDOS:-Non trouvé}"

#===============================================================================
# ANALYSE DES DÉPASSEMENTS DE CAPACITÉ
#===============================================================================

echo ""
echo ">>> Analyse des dépassements de capacité"
echo "-----------------------------------------"

# Recherche des dépassements de capacité avec filtrage
if [ "$IGNORE_DEPASSEMENTS" = "1" ]; then
    echo "⚠️  Dépassements de capacité IGNORÉS par configuration"
    nbinfo=0
    nbinfo_total=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)
    echo "   Dépassements détectés mais ignorés : $nbinfo_total"
else
    # Comptage initial
    nbinfo_total=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | wc -l)

    if [ -n "$IGNORE_CODES" ]; then
        echo "🔍 Filtrage des codes ignorés : $IGNORE_CODES"

        # Créer un fichier temporaire avec les dépassements filtrés
        temp_depassements="$TMP/depassements_filtered_$$.txt"
        grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" > "$temp_depassements"

        # Compter les dépassements après filtrage
        nbinfo=0
        nbinfo_ignores=0

        while read ligne; do
            code_info=$(echo "$ligne" | awk '{print $7}' | cut -c1-4)
            if should_ignore_code "$code_info"; then
                nbinfo_ignores=$((nbinfo_ignores + 1))
                echo "   Code $code_info ignoré"
            else
                nbinfo=$((nbinfo + 1))
            fi
        done < "$temp_depassements"

        rm -f "$temp_depassements"

        echo "   Dépassements total détectés : $nbinfo_total"
        echo "   Dépassements ignorés        : $nbinfo_ignores"
        echo "   Dépassements pris en compte : $nbinfo"
    else
        nbinfo=$nbinfo_total
        echo "   Dépassements détectés : $nbinfo"
    fi

    # Vérification du seuil
    if [ "$nbinfo" -lt "$SEUIL_DEPASSEMENTS" ]; then
        echo "⚠️  Seuil non atteint ($nbinfo < $SEUIL_DEPASSEMENTS) - Dépassements ignorés"
        nbinfo_sous_seuil=$nbinfo
        nbinfo=0
    fi
fi

echo ""
echo ">>> Analyse des messages de débordement"
echo "---------------------------------------"

# Recherche des messages de débordement avec filtrage
if [ "$IGNORE_DEBORDEMENTS" = "1" ]; then
    echo "⚠️  Messages de débordement IGNORÉS par configuration"
    nb_debordement=0
    nb_debordement_total=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)
    echo "   Débordements détectés mais ignorés : $nb_debordement_total"
else
    # Comptage initial
    nb_debordement_total=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | wc -l)

    if [ -n "$IGNORE_MATRICULES" ]; then
        echo "🔍 Filtrage des matricules ignorés : $IGNORE_MATRICULES"

        # Créer un fichier temporaire avec les débordements filtrés
        temp_debordements="$TMP/debordements_filtered_$$.txt"
        grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_debordements"

        # Compter les débordements après filtrage
        nb_debordement=0
        nb_debordement_ignores=0

        while read ligne; do
            matricule=$(echo "$ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')
            if [ -n "$matricule" ] && should_ignore_matricule "$matricule"; then
                nb_debordement_ignores=$((nb_debordement_ignores + 1))
                echo "   Matricule $matricule ignoré"
            else
                nb_debordement=$((nb_debordement + 1))
            fi
        done < "$temp_debordements"

        rm -f "$temp_debordements"

        echo "   Débordements total détectés : $nb_debordement_total"
        echo "   Débordements ignorés        : $nb_debordement_ignores"
        echo "   Débordements pris en compte : $nb_debordement"
    else
        nb_debordement=$nb_debordement_total
        echo "   Débordements détectés : $nb_debordement"
    fi

    # Vérification du seuil
    if [ "$nb_debordement" -lt "$SEUIL_DEBORDEMENTS" ]; then
        echo "⚠️  Seuil non atteint ($nb_debordement < $SEUIL_DEBORDEMENTS) - Débordements ignorés"
        nb_debordement_sous_seuil=$nb_debordement
        nb_debordement=0
    fi
fi

if [ "$nbinfo" -eq 0 ]; then
    echo "✅ Aucun dépassement de capacité détecté"
    echo "   Le traitement s'est déroulé normalement"
else
    echo "⚠️  ATTENTION: $nbinfo dépassement(s) de capacité détecté(s)"
    if [ "$nb_debordement" -gt 0 ]; then
        echo "⚠️  ATTENTION: $nb_debordement message(s) de débordement d'infos étalées détecté(s)"
    fi
    echo ""

    # Extraction des codes d'information uniques
    info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u)
    echo "Codes d'information concernés: $info"
    echo ""

    # Détail des dépassements par code d'information
    echo ">>> Détail des dépassements par code:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | sort -u | while read Information; do
        nb_occur=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -v "WAB-" | awk '{print $7}' | cut -c1-4 | grep -c "^${Information}$")
        echo "  - Code ${Information} (Période: ${PERPAI:-Inconnue}) - ${nb_occur} occurrence(s)"
    done

    echo ""
    echo ">>> Analyse détaillée des dépassements avec contexte:"

    # Création d'un fichier temporaire pour l'analyse séquentielle
    temp_file="$TMP/depassement_analysis_$$.txt"
    grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_file"

    # Analyse des dépassements avec leur contexte
    ligne_precedente=""
    numero_precedent=0
    depassement_count=0
    debordement_associe_count=0
    debordement_isole_count=0

    while IFS=':' read -r numero_ligne contenu_ligne; do
        if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
            depassement_count=$((depassement_count + 1))

            # Extraction des informations détaillées du dépassement
            code_info=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4)
            table_info=$(echo "$contenu_ligne" | grep -o "(TABLE [^)]*)" | sed 's/[()]//g')

            echo ""
            echo "  🔍 DÉPASSEMENT #$depassement_count (ligne $numero_ligne):"
            echo "     Code d'information : $code_info"
            echo "     ${table_info:-Information table non disponible}"
            echo "     Ligne complète     : $contenu_ligne"

            ligne_precedente="$contenu_ligne"
            numero_precedent="$numero_ligne"

        elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
            # Extraction du matricule du message de débordement
            matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')

            if [ -n "$ligne_precedente" ] && [ $((numero_ligne - numero_precedent)) -le 10 ]; then
                debordement_associe_count=$((debordement_associe_count + 1))
                echo "     ➡️  MESSAGE DE DÉBORDEMENT ASSOCIÉ (ligne $numero_ligne):"
                echo "         Matricule concerné : ${matricule:-Non identifié}"
                echo "         Écart avec dépassement : $((numero_ligne - numero_precedent)) ligne(s)"
                echo "         Message complet : $contenu_ligne"
                echo ""
                echo "     📋 RÉSUMÉ DE L'ASSOCIATION :"
                echo "         Dépassement → Code $code_info"
                echo "         Débordement → Matricule ${matricule:-Non identifié}"
                echo "         Impact      → Données non traitées pour ce matricule"
            else
                debordement_isole_count=$((debordement_isole_count + 1))
                echo ""
                echo "  ⚠️  DÉBORDEMENT ISOLÉ #$debordement_isole_count (ligne $numero_ligne):"
                echo "     Matricule concerné : ${matricule:-Non identifié}"
                echo "     Message complet    : $contenu_ligne"
                echo "     Note : Aucun dépassement associé trouvé dans les 10 lignes précédentes"
            fi

            # Réinitialiser pour éviter les associations multiples
            ligne_precedente=""
            numero_precedent=0
        fi
    done < "$temp_file"

    # Résumé de l'analyse
    echo ""
    echo "  📊 RÉSUMÉ DE L'ANALYSE CONTEXTUELLE :"
    echo "     Dépassements détectés        : $depassement_count"
    echo "     Débordements associés        : $debordement_associe_count"
    echo "     Débordements isolés          : $debordement_isole_count"
    echo "     Total débordements           : $((debordement_associe_count + debordement_isole_count))"

    if [ "$debordement_associe_count" -gt 0 ]; then
        echo "     Taux d'association           : $((debordement_associe_count * 100 / depassement_count))%"
    fi

    # Nettoyage du fichier temporaire
    rm -f "$temp_file"

    echo ""
    echo ">>> Lignes complètes des dépassements:"
    grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | while read ligne; do
        echo "  $ligne"
    done

    if [ "$nb_debordement" -gt 0 ]; then
        echo ""
        echo ">>> Tableau récapitulatif des associations:"
        echo "┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐"
        echo "│ Code Info       │ Matricule       │ Ligne Dépass.   │ Ligne Débord.   │"
        echo "├─────────────────┼─────────────────┼─────────────────┼─────────────────┤"

        # Régénération de l'analyse pour le tableau
        temp_table="$TMP/table_analysis_$$.txt"
        grep -n -a "BBAD0012-DEPASSEMENT DE CAPACITE\|NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" > "$temp_table"

        ligne_depass=""
        numero_depass=0
        code_depass=""

        while IFS=':' read -r numero_ligne contenu_ligne; do
            if echo "$contenu_ligne" | grep -q "BBAD0012-DEPASSEMENT DE CAPACITE"; then
                code_depass=$(echo "$contenu_ligne" | awk '{print $7}' | cut -c1-4)
                ligne_depass="$contenu_ligne"
                numero_depass="$numero_ligne"

            elif echo "$contenu_ligne" | grep -q "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES"; then
                matricule=$(echo "$contenu_ligne" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}')

                if [ -n "$ligne_depass" ] && [ $((numero_ligne - numero_depass)) -le 10 ]; then
                    # Association trouvée
                    printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" \
                           "${code_depass:-N/A}" \
                           "${matricule:-N/A}" \
                           "$numero_depass" \
                           "$numero_ligne"
                else
                    # Débordement isolé
                    printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" \
                           "ISOLÉ" \
                           "${matricule:-N/A}" \
                           "N/A" \
                           "$numero_ligne"
                fi

                # Réinitialiser
                ligne_depass=""
                numero_depass=0
                code_depass=""
            fi
        done < "$temp_table"

        echo "└─────────────────┴─────────────────┴─────────────────┴─────────────────┘"
        rm -f "$temp_table"

        echo ""
        echo ">>> Liste des matricules concernés:"
        matricules_uniques=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u)
        if [ -n "$matricules_uniques" ]; then
            total_matricules=$(echo "$matricules_uniques" | wc -l)
            echo "  Total de matricules uniques concernés: $total_matricules"
            echo ""
            echo "$matricules_uniques" | while read matricule; do
                nb_occurrences=$(grep -a "MATRICULE $matricule" "${Fich_Log}" | grep -c "NON TRAITE")
                echo "  - $matricule (${nb_occurrences} occurrence(s))"
            done
        fi

        echo ""
        echo ">>> Lignes complètes des messages de débordement:"
        grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | while read ligne; do
            echo "  $ligne"
        done
    fi
fi

#===============================================================================
# ANALYSE COMPLÉMENTAIRE ET STATISTIQUES
#===============================================================================

echo ""
echo ">>> Statistiques du fichier de log"
echo "-----------------------------------"

# Statistiques générales du fichier avec filtrage
nb_lignes=$(wc -l < "${Fich_Log}")

# Gestion des erreurs avec filtrage
if [ "$IGNORE_ERRORS" = "1" ]; then
    echo "⚠️  Erreurs générales IGNORÉES par configuration"
    nb_erreurs=0
    nb_erreurs_total=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Erreurs détectées mais ignorées : $nb_erreurs_total"
else
    nb_erreurs=$(grep -c -i "error\|erreur\|échec\|failed" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Erreurs détectées : $nb_erreurs"
fi

# Gestion des avertissements avec filtrage
if [ "$IGNORE_WARNINGS" = "1" ]; then
    echo "⚠️  Avertissements IGNORÉS par configuration"
    nb_warnings=0
    nb_warnings_total=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Avertissements détectés mais ignorés : $nb_warnings_total"
else
    nb_warnings=$(grep -c -i "warning\|attention\|avertissement" "${Fich_Log}" 2>/dev/null || echo "0")
    echo "   Avertissements détectés : $nb_warnings"
fi



echo "Nombre total de lignes       : $nb_lignes"
echo "Nombre d'erreurs détectées   : $nb_erreurs"
echo "Nombre d'avertissements      : $nb_warnings"
echo "Dépassements de capacité     : $nbinfo"
echo "Messages de débordement      : $nb_debordement"

# Recherche d'autres informations utiles
nb_bbao=$(grep -c "BBAO" "${Fich_Log}" 2>/dev/null || echo "0")
nb_bbad=$(grep -c "BBAD" "${Fich_Log}" 2>/dev/null || echo "0")

echo ""
echo ">>> Codes de traitement détectés"
echo "---------------------------------"
echo "Messages BBAO (informations) : $nb_bbao"
echo "Messages BBAD (données)      : $nb_bbad"

# Recherche des codes d'erreur spécifiques
if [ "$nb_erreurs" -gt 0 ]; then
    echo ""
    echo ">>> Erreurs détectées dans le log:"
    grep -i "error\|erreur\|échec\|failed" "${Fich_Log}" | head -10 | while read ligne_erreur; do
        echo "  $ligne_erreur"
    done
    if [ "$nb_erreurs" -gt 10 ]; then
        echo "  ... et $((nb_erreurs - 10)) autres erreurs"
    fi
fi

#===============================================================================
# RÉSUMÉ ET RECOMMANDATIONS
#===============================================================================

echo ""
echo ">>> Résumé de l'analyse"
echo "========================"

# Détermination du statut global
if [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ] && [ "$nb_erreurs" -eq 0 ]; then
    statut="✅ SUCCÈS"
    recommandation="Le traitement s'est déroulé sans problème."
elif [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    if [ "$nb_erreurs" -eq 0 ]; then
        statut="⚠️  ATTENTION"
        if [ "$nb_debordement" -gt 0 ]; then
            recommandation="Dépassements de capacité avec débordement d'infos étalées détectés. Vérifier la configuration et l'espace disponible."
        else
            recommandation="Dépassements de capacité détectés. Vérifier les paramètres de configuration."
        fi
    else
        statut="❌ ERREUR CRITIQUE"
        recommandation="Dépassements de capacité ET erreurs détectés. Investigation urgente requise."
    fi
elif [ "$nb_erreurs" -gt 0 ]; then
    statut="❌ ERREUR"
    recommandation="Erreurs détectées dans le traitement. Investigation requise."
else
    statut="ℹ️  INFORMATION"
    recommandation="Analyse terminée. Consulter les détails ci-dessus."
fi

echo "Statut              : $statut"
echo "Période traitée     : ${PERPAI:-Inconnue}"
echo "Type de paie        : ${TYPPAI:-Inconnu}"
echo "Dépassements        : $nbinfo"
echo "Messages débordement: $nb_debordement"
echo "Erreurs             : $nb_erreurs"
echo "Recommandation      : $recommandation"

# Logging des résultats d'analyse
log_message "=== RÉSULTATS D'ANALYSE ==="
log_message "Statut: $statut"
log_message "Période: ${PERPAI:-Inconnue}"
log_message "Type paie: ${TYPPAI:-Inconnu}"
log_message "Dépassements: $nbinfo"
log_message "Débordements: $nb_debordement"
log_message "Erreurs: $nb_erreurs"
log_message "Durée analyse: ${DUREE:-En cours}s"

# Résumé des filtres appliqués
echo ""
echo ">>> Résumé des filtres appliqués:"
echo "================================="
filtres_actifs=0

if [ "$IGNORE_DEPASSEMENTS" = "1" ]; then
    echo "🔇 Dépassements de capacité : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_DEBORDEMENTS" = "1" ]; then
    echo "🔇 Messages de débordement : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ -n "$IGNORE_CODES" ]; then
    echo "🔇 Codes ignorés : $IGNORE_CODES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ -n "$IGNORE_MATRICULES" ]; then
    echo "🔇 Matricules ignorés : $IGNORE_MATRICULES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$SEUIL_DEPASSEMENTS" -gt 1 ]; then
    echo "📊 Seuil dépassements : $SEUIL_DEPASSEMENTS (minimum pour alerter)"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$SEUIL_DEBORDEMENTS" -gt 1 ]; then
    echo "📊 Seuil débordements : $SEUIL_DEBORDEMENTS (minimum pour alerter)"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_ERRORS" = "1" ]; then
    echo "🔇 Erreurs générales : IGNORÉES"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$IGNORE_WARNINGS" = "1" ]; then
    echo "🔇 Avertissements : IGNORÉS"
    filtres_actifs=$((filtres_actifs + 1))
fi

if [ "$filtres_actifs" -eq 0 ]; then
    echo "✅ Aucun filtre actif - Analyse complète"
else
    echo ""
    echo "⚠️  $filtres_actifs filtre(s) actif(s) - Certains problèmes peuvent être masqués"
fi

# Recommandations spécifiques basées sur l'analyse
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
    echo ""
    echo ">>> Recommandations spécifiques:"
    echo "================================"

    if [ "$nb_debordement" -gt 0 ]; then
        # Extraction des matricules pour recommandations
        matricules_concernes=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u)
        nb_matricules=$(echo "$matricules_concernes" | wc -l)

        echo "🔴 ACTIONS URGENTES :"
        echo "  1. Vérifier le traitement de paie pour $nb_matricules matricule(s)"
        echo "  2. Contrôler les données de paie des matricules suivants :"
        echo "$matricules_concernes" | head -5 | while read mat; do
            echo "     - $mat"
        done
        if [ "$nb_matricules" -gt 5 ]; then
            echo "     ... et $((nb_matricules - 5)) autres matricules (voir liste complète ci-dessus)"
        fi
        echo ""
        echo "🔧 ACTIONS TECHNIQUES :"
        echo "  1. Augmenter l'espace de stockage pour éviter les débordements"
        echo "  2. Optimiser les paramètres de capacité des tables"
        echo "  3. Prévoir un retraitement pour les matricules non traités"
        echo ""
        echo "📋 CONTRÔLES À EFFECTUER :"
        echo "  1. Vérifier la complétude des bulletins de paie"
        echo "  2. Contrôler les montants calculés pour les matricules concernés"
        echo "  3. Valider les déclarations sociales"

    else
        echo "⚠️  SURVEILLANCE :"
        echo "  1. Surveiller les prochains traitements de paie"
        echo "  2. Vérifier la tendance d'évolution des dépassements"
        echo "  3. Prévoir une optimisation préventive"
    fi

    echo ""
    echo "📞 ESCALADE :"
    if [ "$nb_debordement" -gt 0 ]; then
        echo "  Niveau : URGENT - Contacter immédiatement l'équipe technique"
        echo "  Impact : Données de paie potentiellement incomplètes"
    else
        echo "  Niveau : NORMAL - Informer l'équipe technique lors du prochain créneau"
        echo "  Impact : Surveillance préventive recommandée"
    fi
fi

#===============================================================================
# ENVOI D'EMAIL AUTOMATIQUE
#===============================================================================

# Calcul du temps d'exécution
DATFIN=$(date +'%H:%M:%S')
FINTRT=$(date +'%s')
DUREE=$((FINTRT - DEBTRT))

# Envoi d'email seulement si problèmes détectés
if ([ "$SEND_EMAIL" = "1" ] || [ -n "$EMAIL_DEST" ]) && ([ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]); then
    echo ""
    echo ">>> Envoi d'email automatique"
    echo "============================="

    # Logging de la tentative d'envoi
    log_email "TENTATIVE_ENVOI" "Destinataires: $EMAIL_DEST, Sujet: $EMAIL_SUBJECT"
    log_message "Tentative d'envoi email - Problèmes détectés: Dépassements=$nbinfo, Débordements=$nb_debordement"

    # Vérification de la commande mail
    if ! command -v mail >/dev/null 2>&1; then
        echo "❌ Commande 'mail' non disponible"
        echo "   Installation : apt-get install mailutils (Debian/Ubuntu)"
        echo "   ou           : yum install mailx (CentOS/RHEL)"
        log_email "ERREUR_COMMANDE" "Commande 'mail' non disponible sur le système"
        log_message "ERREUR: Commande mail non disponible"
        return 1
    fi

    echo "Destinataires : $EMAIL_DEST"
    echo "Sujet         : $EMAIL_SUBJECT"

    # Génération du contenu de l'email
    EMAIL_CONTENT="$TMP/email_content_$$.txt"

    {
        echo "==============================================================================="
        echo "RAPPORT D'ANALYSE DES DÉPASSEMENTS DE CAPACITÉ"
        echo "==============================================================================="
        echo "Fichier analysé : $(basename "$Fich_Log")"
        echo "Date d'analyse  : $(date +'%d/%m/%Y à %H:%M:%S')"
        echo "Durée d'analyse : ${DUREE}s"
        echo "Environnement   : ${ENVIRONNEMENT:-prod}"
        echo ""

        echo "RÉSUMÉ EXÉCUTIF :"
        echo "================"
        echo "Statut : $statut"
        echo "Recommandation : $recommandation"
        echo ""

        echo "STATISTIQUES :"
        echo "=============="
        echo "• Période de paie        : ${PERPAI:-Inconnue}"
        echo "• Type de paie           : ${TYPPAI:-Inconnu}"
        echo "• Dépassements détectés  : $nbinfo"
        echo "• Débordements détectés  : $nb_debordement"
        echo "• Erreurs générales      : $nb_erreurs"
        echo "• Avertissements         : $nb_warnings"
        echo "• Total lignes analysées : $nb_lignes"
        echo ""

        if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ]; then
            echo "DÉTAILS DES PROBLÈMES :"
            echo "======================"

            if [ "$nbinfo" -gt 0 ]; then
                echo ""
                echo "🔍 DÉPASSEMENTS DE CAPACITÉ ($nbinfo détecté(s)) :"
                echo "---------------------------------------------------"

                # Extraction des codes d'information
                codes_info=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | awk '{print $7}' | cut -c1-4 | sort -u | head -5)
                if [ -n "$codes_info" ]; then
                    echo "Codes d'information concernés :"
                    echo "$codes_info" | while read code; do
                        nb_code=$(grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | grep -c "$code")
                        echo "  - $code ($nb_code occurrence(s))"
                    done
                fi

                echo ""
                echo "Exemples de dépassements (5 premiers) :"
                grep -a "BBAD0012-DEPASSEMENT DE CAPACITE" "${Fich_Log}" | head -5 | while read ligne; do
                    echo "  $ligne"
                done
            fi

            if [ "$nb_debordement" -gt 0 ]; then
                echo ""
                echo "💾 MESSAGES DE DÉBORDEMENT ($nb_debordement détecté(s)) :"
                echo "--------------------------------------------------------"

                # Extraction des matricules
                matricules=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u | head -10)
                if [ -n "$matricules" ]; then
                    total_matricules=$(grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | grep -o "MATRICULE [A-Z0-9]*" | awk '{print $2}' | sort -u | wc -l)
                    echo "Matricules concernés ($total_matricules au total) :"
                    echo "$matricules" | while read matricule; do
                        echo "  - $matricule"
                    done
                    if [ "$total_matricules" -gt 10 ]; then
                        echo "  ... et $((total_matricules - 10)) autres matricules"
                    fi
                fi

                echo ""
                echo "Exemples de débordements (3 premiers) :"
                grep -a "NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES" "${Fich_Log}" | head -3 | while read ligne; do
                    echo "  $ligne"
                done
            fi

            echo ""
            echo "ACTIONS RECOMMANDÉES :"
            echo "====================="
            if [ "$nb_debordement" -gt 0 ]; then
                echo "🔴 URGENT :"
                echo "  • Vérifier le traitement de paie pour les matricules listés"
                echo "  • Contrôler la complétude des bulletins de paie"
                echo "  • Prévoir un retraitement si nécessaire"
                echo ""
                echo "🔧 TECHNIQUE :"
                echo "  • Augmenter l'espace de stockage"
                echo "  • Optimiser les paramètres de capacité"
                echo "  • Surveiller les prochains traitements"
            else
                echo "⚠️  SURVEILLANCE :"
                echo "  • Surveiller l'évolution des dépassements"
                echo "  • Prévoir une optimisation préventive"
            fi
        else
            echo "✅ AUCUN PROBLÈME DÉTECTÉ"
            echo "========================="
            echo "Le traitement de paie s'est déroulé sans dépassement ni débordement."
        fi

        # Configuration des filtres si appliqués
        filtres_actifs=0
        if [ "$IGNORE_DEPASSEMENTS" = "1" ] || [ "$IGNORE_DEBORDEMENTS" = "1" ] || [ -n "$IGNORE_CODES" ] || [ -n "$IGNORE_MATRICULES" ] || [ "$SEUIL_DEPASSEMENTS" -gt 1 ] || [ "$SEUIL_DEBORDEMENTS" -gt 1 ]; then
            echo ""
            echo "CONFIGURATION DES FILTRES :"
            echo "=========================="

            if [ "$IGNORE_DEPASSEMENTS" = "1" ]; then
                echo "• Dépassements ignorés : OUI"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            if [ "$IGNORE_DEBORDEMENTS" = "1" ]; then
                echo "• Débordements ignorés : OUI"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            if [ -n "$IGNORE_CODES" ]; then
                echo "• Codes ignorés : $IGNORE_CODES"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            if [ -n "$IGNORE_MATRICULES" ]; then
                echo "• Matricules ignorés : $IGNORE_MATRICULES"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            if [ "$SEUIL_DEPASSEMENTS" -gt 1 ]; then
                echo "• Seuil dépassements : $SEUIL_DEPASSEMENTS"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            if [ "$SEUIL_DEBORDEMENTS" -gt 1 ]; then
                echo "• Seuil débordements : $SEUIL_DEBORDEMENTS"
                filtres_actifs=$((filtres_actifs + 1))
            fi

            echo ""
            echo "⚠️  $filtres_actifs filtre(s) actif(s) - Certains problèmes peuvent être masqués"
        fi

        echo ""
        echo "==============================================================================="
        echo "Rapport généré automatiquement par Check_depordement.ksh"
        echo "Équipe TechOps - $(date +'%Y-%m-%d %H:%M:%S')"
        echo "==============================================================================="

    } > "$EMAIL_CONTENT"

    # Envoi de l'email
    if mail -s "$EMAIL_SUBJECT" "$EMAIL_DEST" < "$EMAIL_CONTENT" 2>/dev/null; then
        EMAIL_SIZE=$(wc -c < "$EMAIL_CONTENT")
        echo "✅ Email envoyé avec succès"
        echo "   Destinataires : $EMAIL_DEST"
        echo "   Taille : $EMAIL_SIZE caractères"

        # Logging du succès
        log_email "ENVOI_REUSSI" "Destinataires: $EMAIL_DEST, Taille: $EMAIL_SIZE caractères"
        log_message "Email envoyé avec succès à $EMAIL_DEST"

        # Sauvegarde du contenu email dans le log
        echo "# Contenu de l'email envoyé le $(date +'%Y-%m-%d %H:%M:%S')" >> "$EMAIL_LOG_FILE"
        echo "# Destinataires: $EMAIL_DEST" >> "$EMAIL_LOG_FILE"
        echo "# Sujet: $EMAIL_SUBJECT" >> "$EMAIL_LOG_FILE"
        echo "# Taille: $EMAIL_SIZE caractères" >> "$EMAIL_LOG_FILE"
        echo "# ===========================================" >> "$EMAIL_LOG_FILE"
        cat "$EMAIL_CONTENT" >> "$EMAIL_LOG_FILE"
        echo "" >> "$EMAIL_LOG_FILE"
        echo "# ===========================================" >> "$EMAIL_LOG_FILE"
        echo "" >> "$EMAIL_LOG_FILE"

    else
        echo "❌ Erreur lors de l'envoi de l'email"
        echo "   Vérifier la configuration du serveur mail"
        echo "   Contenu sauvegardé dans : $EMAIL_CONTENT"

        # Logging de l'échec
        log_email "ENVOI_ECHEC" "Destinataires: $EMAIL_DEST, Erreur système"
        log_message "ERREUR: Échec envoi email à $EMAIL_DEST - Contenu sauvé: $EMAIL_CONTENT"
    fi

    # Nettoyage (optionnel, garder pour debug si erreur)
    # rm -f "$EMAIL_CONTENT"
elif [ -n "$EMAIL_DEST" ] && [ "$nbinfo" -eq 0 ] && [ "$nb_debordement" -eq 0 ]; then
    echo ""
    echo ">>> Email configuré mais non envoyé"
    echo "=================================="
    echo "Destinataires : $EMAIL_DEST"
    echo "Raison        : Aucun problème détecté (pas de dépassement ni débordement)"
    echo "ℹ️  L'email n'est envoyé qu'en cas de problème"

    # Logging du non-envoi
    log_email "NON_ENVOYE" "Destinataires: $EMAIL_DEST, Raison: Aucun problème détecté"
    log_message "Email configuré mais non envoyé - Aucun problème détecté"
fi

# Logging de fin d'analyse
log_message "=== FIN D'ANALYSE ==="
log_message "Durée totale: ${DUREE}s"
log_message "Statut final: $statut"




echo ""
echo "==============================================================================="
echo "Fin de l'analyse des dépassements de capacité"
echo "==============================================================================="
echo "Fichier analysé : $Fich_Log"
echo "Heure début     : $DATDEB"
echo "Heure fin       : $DATFIN"
echo "Durée           : ${DUREE}s"
echo "Statut final    : $statut"
echo "==============================================================================="

# Code de retour selon le résultat
if [ "$nbinfo" -gt 0 ] || [ "$nb_debordement" -gt 0 ] || [ "$nb_erreurs" -gt 0 ]; then
    exit 1  # Problème détecté (dépassement, débordement ou erreur)
else
    exit 0  # Tout va bien
fi
