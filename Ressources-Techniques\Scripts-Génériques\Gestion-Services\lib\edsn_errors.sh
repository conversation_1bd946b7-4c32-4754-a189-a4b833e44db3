#!/bin/ksh
#===============================================================================
# Module de gestion des erreurs eDSN
#===============================================================================
# Description : Fonctions pour la gestion des erreurs et contrôles eDSN
# Version     : 2.0
#===============================================================================

#===============================================================================
# CONTRÔLE DES PROCESSUS EN ERREUR
#===============================================================================

# Fonction principale pour contrôler les processus en erreur
cmd_ctlwferr() {
    local periode="$1"
    
    log_info "Début du contrôle des processus en erreur pour la période $periode"
    
    # Liste des processus en erreur
    local list_file="$TMP/${PREFIX_CTLWFERR}_list_DSNERR_${periode}.txt"
    local detail_file="$TMP/${PREFIX_CTLWFERR}_detail_process_DSNERR_${periode}.txt"
    
    log_info "Récupération de la liste des processus en erreur"
    LaunchEDSN "dsnworkflow:list-processes -o $list_file -e ${periode}"
    
    if [ $? -ne 0 ]; then
        log_error "Échec de la récupération de la liste des processus en erreur"
        return 1
    fi
    
    # Filtrage des déclarations de contrôle
    grep "Declaration de controle" "$list_file" | awk '{print $1}' > "${list_file}2"
    mv "${list_file}2" "$list_file"
    
    if [ -s "$list_file" ]; then
        log_info "$(wc -l < $list_file) processus en erreur trouvés"
        
        # Détail de chaque processus en erreur
        while read nuproc; do
            log_debug "Analyse du processus en erreur: $nuproc"
            LaunchEDSN "dsnworkflow:detail-process $nuproc"
        done < "$list_file"
        
        # Sauvegarde du détail
        if [ -n "$FICLOG" ] && [ -f "$FICLOG" ]; then
            cp "${FICLOG}" "$detail_file"
            log_info "Détail des processus en erreur sauvegardé dans: $detail_file"
        fi
    else
        log_info "Aucun processus en erreur trouvé pour la période $periode"
    fi
    
    log_info "Fin du contrôle des processus en erreur"
    return 0
}

#===============================================================================
# SUPPRESSION DES PROCESSUS EN ERREUR
#===============================================================================

# Fonction pour supprimer les processus en erreur
cmd_delwferr() {
    local periode="$1"
    
    log_info "Début de la suppression des processus en erreur pour la période $periode"
    
    local list_file="$TMP/DELWFERR_list_DSNERR_${periode}.txt"
    
    if [ ! -s "$list_file" ]; then
        log_error "Fichier de liste des processus en erreur introuvable: $list_file"
        log_error "Exécutez d'abord CTLWFERR pour générer la liste"
        return 1
    fi
    
    log_info "Suppression des processus listés dans: $list_file"
    LaunchEDSN "dsntest:cancel${newcmd}byprocessid ${newcmd221} -b -f @$list_file"
    
    if [ $? -eq 0 ]; then
        log_info "Suppression des processus en erreur terminée avec succès"
    else
        log_error "Échec de la suppression des processus en erreur"
        return 1
    fi
    
    return 0
}

#===============================================================================
# REPRISE DES ERREURS M2M
#===============================================================================

# Fonction pour reprendre les erreurs M2M (version simple)
cmd_repm2merr() {
    local periode="$1"
    
    log_info "Début de la reprise des erreurs M2M pour la période $periode"
    
    local list_file="$TMP/CTL_list_M2MERR_${periode}.txt"
    
    # Liste des processus M2M en erreur
    LaunchEDSN "dsnworkflow:list-m2m-send-error-processes -x -o $list_file ${periode}"
    
    if [ $? -ne 0 ]; then
        log_error "Échec de la récupération des processus M2M en erreur"
        return 1
    fi
    
    if [ -s "$list_file" ]; then
        local nb_errors=$(wc -l < "$list_file")
        log_info "$nb_errors processus M2M en erreur trouvés"
        
        # Relance des processus en erreur
        LaunchEDSN "dsnworkflow:retry-erroneous-processes @$list_file"
        
        if [ $? -eq 0 ]; then
            log_info "Reprise des processus M2M en erreur terminée avec succès"
        else
            log_error "Échec de la reprise des processus M2M en erreur"
            return 1
        fi
    else
        log_info "Aucun processus M2M en erreur trouvé pour la période $periode"
    fi
    
    return 0
}

# Fonction pour reprendre les erreurs M2M (version avancée)
cmd_repm2merr2() {
    local periode="$1"
    
    log_info "Début de la reprise avancée des erreurs M2M pour la période $periode"
    
    local rcgpo=0
    
    # Traitement pour chaque type d'envoi
    for typeEnvoi in $PROCESS_M2M $PROCESS_M2M_SIG; do
        log_info "Traitement du type d'envoi: $typeEnvoi"
        
        local list_file="$TMP/CTL_list_M2MERR_${periode}.txt"
        
        # Liste des processus en erreur pour ce type
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $list_file -t $typeEnvoi" </dev/null
        
        if [ -s "$list_file" ]; then
            local nb_errors=$(wc -l < "$list_file")
            log_info "$nb_errors processus $typeEnvoi en erreur trouvés"
            
            # Relance des processus en erreur
            LaunchEDSN "dsnworkflow:retry-erroneous-processes @$list_file" </dev/null
            
            # Liste des jobs de dépôt M2M
            local job_file="$TMP/CTL_list_job1_${periode}.txt"
            LaunchEDSN "async:list-jobs -x -o $job_file -t $typeEnvoi" </dev/null
            
            if [ -s "$job_file" ]; then
                local n1=$(wc -l < "$job_file")
                log_info "$n1 reprises de dépôt $typeEnvoi à traiter"
                
                LaunchEDSN "async:wait-jobs @$job_file" </dev/null
                rm -f "$job_file"
                log_info "Reprises $typeEnvoi effectuées"
            fi
        else
            log_info "Aucune reprise de dépôt $typeEnvoi à faire"
        fi
        
        # Post-contrôle des reprises non faites
        LaunchEDSN "dsnworkflow:list-processes -e -x -o $list_file -t $typeEnvoi" </dev/null
        
        if [ -s "$list_file" ]; then
            log_warn "Il reste des reprises $typeEnvoi à réaliser"
            LaunchEDSN "dsnworkflow:list-processes -e -t $typeEnvoi" </dev/null
            rcgpo=$((rcgpo + RC + 1))
        fi
    done
    
    # Notification du résultat
    if [ $rcgpo -gt 0 ]; then
        log_error "Reprise en échec ou incomplète"
        if [ -n "$SRVNET_DIR" ] && [ -n "$Slice" ]; then
            $SRVNET_DIR/U_POST_UPROC_${Slice} "KO reprise en échec ou incomplète" "$DATDEB" $DEBTRT "$Description"
        fi
        return 1
    else
        log_info "Reprise avancée des erreurs M2M terminée avec succès"
        return 0
    fi
}

#===============================================================================
# VÉRIFICATION DES COMPTES
#===============================================================================

# Fonction pour vérifier les comptes déclarants
cmd_verifcompte() {
    log_info "Début de la vérification des comptes déclarants"
    
    local list_file="$TMP/listedecl.txt"
    
    # Liste des déclarants
    LaunchEDSN "m2m:list-declarants -o $list_file"
    
    if [ $? -ne 0 ]; then
        log_error "Échec de la récupération de la liste des déclarants"
        return 1
    fi
    
    if [ ! -s "$list_file" ]; then
        log_warn "Aucun déclarant trouvé"
        return 0
    fi
    
    local nb_declarants=$(wc -l < "$list_file")
    log_info "$nb_declarants déclarants à vérifier"
    
    # Vérification de chaque déclarant
    while read line; do
        local actif=$(echo "$line" | awk -F '|' '{print $2}')
        local habilitation=$(echo "$line" | awk -F '|' '{print $6}')
        local id_compte=$(echo "$line" | awk -F '|' '{print $1}')
        
        log_debug "Vérification compte: $id_compte (actif=$actif, habilitation=$habilitation)"
        
        if [ "$actif" = "x" ]; then
            if [ "$habilitation" = "NET / MSA" ]; then
                # Gestion particulière point dépôt NET / MSA
                log_debug "Test compte point dépôt NET pour $id_compte"
                LaunchEDSN "m2m:ping-gip -d '$id_compte' NET"
                
                log_debug "Test compte point dépôt MSA pour $id_compte"
                LaunchEDSN "m2m:ping-gip -d '$id_compte' MSA"
            else
                # Test compte pour point dépôt correspondant
                log_debug "Test compte point dépôt $habilitation pour $id_compte"
                LaunchEDSN "m2m:ping-gip -d '$id_compte' $habilitation"
            fi
        else
            log_debug "Compte $id_compte inactif, ignoré"
        fi
    done < "$list_file"
    
    log_info "Vérification des comptes déclarants terminée"
    return 0
}
