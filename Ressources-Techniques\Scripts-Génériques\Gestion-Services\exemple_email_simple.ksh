#!/bin/ksh
#===============================================================================
# Exemples d'utilisation simple avec envoi d'email - Check_depordement.ksh
#===============================================================================
# Description : Exemples pratiques d'utilisation simplifiée avec email
# Version     : 1.0
#===============================================================================

echo "==============================================================================="
echo "Exemples d'utilisation simple avec envoi d'email - Check_depordement.ksh"
echo "==============================================================================="
echo ""

# Configuration de base
export LOG="/u02/app/hraccess/prod/txt/log"
export TMP="/tmp"

echo "1. ANALYSE SIMPLE SANS EMAIL :"
echo "   ./Check_depordement.ksh FICHIER.log"
echo ""
echo "   → Analyse le fichier et affiche les résultats à l'écran"
echo "   → Sortie simple et claire"
echo "   → Aucun fichier généré"
echo ""

echo "2. ANALYSE AVEC ENVOI D'EMAIL AUTOMATIQUE :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   ./Check_depordement.ksh FICHIER.log"
echo ""
echo "   → Analyse le fichier"
echo "   → Envoie automatiquement un email avec le rapport"
echo "   → Email contient toutes les informations importantes"
echo ""

echo "3. ENVOI À PLUSIEURS DESTINATAIRES :"
echo "   export EMAIL_DEST=\"<EMAIL>,<EMAIL>,<EMAIL>\""
echo "   ./Check_depordement.ksh FICHIER.log"
echo ""
echo "   → Envoie le rapport à plusieurs personnes"
echo "   → Séparer les adresses par des virgules"
echo ""

echo "4. PERSONNALISATION DU SUJET :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   export EMAIL_SUBJECT=\"[URGENT] Problème de paie détecté\""
echo "   ./Check_depordement.ksh FICHIER.log"
echo ""
echo "   → Personnalise le sujet de l'email"
echo "   → Utile pour différencier les environnements"
echo ""

echo "5. SCRIPT DE SURVEILLANCE AUTOMATIQUE :"
cat << 'EOF'
   #!/bin/bash
   # Script de surveillance quotidienne simple
   
   export EMAIL_DEST="<EMAIL>"
   export EMAIL_SUBJECT="Rapport quotidien de paie - $(date +%d/%m/%Y)"
   
   for log_file in /var/log/paie/FFCALK3W.*; do
       if [ -f "$log_file" ]; then
           echo "Analyse de $(basename "$log_file")"
           ./Check_depordement.ksh "$(basename "$log_file")"
       fi
   done
EOF
echo ""

echo "6. SCRIPT AVEC GESTION D'ERREUR :"
cat << 'EOF'
   #!/bin/bash
   # Script robuste avec gestion d'erreur
   
   LOG_FILE="$1"
   
   if [ -z "$LOG_FILE" ]; then
       echo "Usage: $0 FICHIER.log"
       exit 1
   fi
   
   if [ ! -f "$LOG_FILE" ]; then
       echo "Erreur: Fichier $LOG_FILE introuvable"
       exit 1
   fi
   
   # Configuration email
   export EMAIL_DEST="<EMAIL>"
   export EMAIL_SUBJECT="Analyse de paie - $(basename "$LOG_FILE")"
   
   # Analyse
   ./Check_depordement.ksh "$LOG_FILE"
   
   if [ $? -eq 0 ]; then
       echo "✅ Analyse terminée avec succès"
   else
       echo "⚠️  Problèmes détectés - Vérifier l'email"
   fi
EOF
echo ""

echo "7. SCRIPT AVEC FILTRAGE ET EMAIL :"
cat << 'EOF'
   #!/bin/bash
   # Script avec filtres personnalisés
   
   # Configuration des filtres
   export IGNORE_CODES="TEST,DEMO"
   export SEUIL_DEPASSEMENTS=5
   export SEUIL_DEBORDEMENTS=3
   
   # Configuration email
   export EMAIL_DEST="<EMAIL>"
   export EMAIL_SUBJECT="Rapport filtré de paie"
   
   # Analyse avec filtres
   ./Check_depordement.ksh "$1"
EOF
echo ""

echo "==============================================================================="
echo "Contenu de l'email envoyé :"
echo "==============================================================================="
echo ""

echo "STRUCTURE DE L'EMAIL :"
echo "====================="
echo ""
echo "📧 SUJET :"
echo "   Rapport d'analyse de paie (ou personnalisé)"
echo ""
echo "📄 CONTENU :"
echo "   ==============================================================================="
echo "   RAPPORT D'ANALYSE DES DÉPASSEMENTS DE CAPACITÉ"
echo "   ==============================================================================="
echo "   Fichier analysé : FFCALK3W_RJ2.2925"
echo "   Date d'analyse  : 15/12/2023 à 14:30:25"
echo "   Durée d'analyse : 12s"
echo "   Environnement   : prod"
echo ""
echo "   RÉSUMÉ EXÉCUTIF :"
echo "   ================"
echo "   Statut : ✅ SUCCÈS - Aucun problème critique détecté"
echo "   Recommandation : Surveillance préventive recommandée"
echo ""
echo "   STATISTIQUES :"
echo "   =============="
echo "   • Période de paie        : 202312"
echo "   • Type de paie           : Mensuelle"
echo "   • Dépassements détectés  : 2"
echo "   • Débordements détectés  : 0"
echo "   • Erreurs générales      : 0"
echo "   • Avertissements         : 1"
echo "   • Total lignes analysées : 15847"
echo ""

echo "SI PROBLÈMES DÉTECTÉS :"
echo "======================="
echo ""
echo "   DÉTAILS DES PROBLÈMES :"
echo "   ======================"
echo ""
echo "   🔍 DÉPASSEMENTS DE CAPACITÉ (2 détecté(s)) :"
echo "   ---------------------------------------------------"
echo "   Codes d'information concernés :"
echo "     - ZYWA (1 occurrence(s))"
echo "     - ABCD (1 occurrence(s))"
echo ""
echo "   Exemples de dépassements (5 premiers) :"
echo "     [Lignes exactes du log avec détails techniques]"
echo ""
echo "   💾 MESSAGES DE DÉBORDEMENT (0 détecté(s)) :"
echo "   --------------------------------------------------------"
echo "   [Si applicable : matricules concernés et exemples]"
echo ""
echo "   ACTIONS RECOMMANDÉES :"
echo "   ====================="
echo "   🔴 URGENT :"
echo "     • Vérifier le traitement de paie pour les matricules listés"
echo "     • Contrôler la complétude des bulletins de paie"
echo "   🔧 TECHNIQUE :"
echo "     • Augmenter l'espace de stockage"
echo "     • Optimiser les paramètres de capacité"
echo ""

echo "SI AUCUN PROBLÈME :"
echo "=================="
echo ""
echo "   ✅ AUCUN PROBLÈME DÉTECTÉ"
echo "   ========================="
echo "   Le traitement de paie s'est déroulé sans dépassement ni débordement."
echo ""

echo "CONFIGURATION DES FILTRES (si applicable) :"
echo "==========================================="
echo ""
echo "   CONFIGURATION DES FILTRES :"
echo "   =========================="
echo "   • Codes ignorés : TEST,DEMO"
echo "   • Seuil dépassements : 5"
echo "   ⚠️  2 filtre(s) actif(s) - Certains problèmes peuvent être masqués"
echo ""

echo "==============================================================================="
echo "Prérequis pour l'envoi d'email :"
echo "==============================================================================="
echo ""

echo "INSTALLATION DE LA COMMANDE MAIL :"
echo "=================================="
echo ""
echo "Debian/Ubuntu :"
echo "  sudo apt-get install mailutils"
echo ""
echo "CentOS/RHEL :"
echo "  sudo yum install mailx"
echo ""
echo "Vérification :"
echo "  command -v mail"
echo "  echo \"Test\" | mail -s \"Test\" <EMAIL>"
echo ""

echo "CONFIGURATION DU SERVEUR MAIL :"
echo "==============================="
echo ""
echo "Le script utilise la commande 'mail' du système."
echo "Assurez-vous que :"
echo "  • Un serveur SMTP est configuré sur le système"
echo "  • Les paramètres de relais sont corrects"
echo "  • Les adresses email de destination sont valides"
echo ""

echo "DÉPANNAGE COURANT :"
echo "=================="
echo ""
echo "❌ Commande 'mail' non disponible :"
echo "   → Installer mailutils ou mailx"
echo ""
echo "❌ Email non reçu :"
echo "   → Vérifier la configuration SMTP"
echo "   → Contrôler les logs système (/var/log/mail.log)"
echo "   → Tester avec : echo \"Test\" | mail -s \"Test\" <EMAIL>"
echo ""
echo "❌ Erreur lors de l'envoi :"
echo "   → Le contenu est sauvegardé dans /tmp/email_content_*.txt"
echo "   → Vérifier les permissions et l'espace disque"
echo ""

echo "==============================================================================="
echo "Exemples de sortie du script :"
echo "==============================================================================="
echo ""

echo "SANS EMAIL (sortie normale) :"
echo "============================="
echo ""
echo ">>> Analyse terminée"
echo "Statut final : ✅ SUCCÈS - Aucun problème critique détecté"
echo "Recommandation : Surveillance préventive recommandée"
echo ""

echo "AVEC EMAIL ACTIVÉ :"
echo "==================="
echo ""
echo ">>> Envoi d'email automatique"
echo "============================="
echo "Destinataires : <EMAIL>"
echo "Sujet         : Rapport d'analyse de paie"
echo "✅ Email envoyé avec succès"
echo "   Destinataires : <EMAIL>"
echo "   Taille : 2847 caractères"
echo ""

echo "EN CAS D'ERREUR EMAIL :"
echo "======================="
echo ""
echo ">>> Envoi d'email automatique"
echo "============================="
echo "❌ Commande 'mail' non disponible"
echo "   Installation : apt-get install mailutils (Debian/Ubuntu)"
echo "   ou           : yum install mailx (CentOS/RHEL)"
echo ""
echo "ou"
echo ""
echo "❌ Erreur lors de l'envoi de l'email"
echo "   Vérifier la configuration du serveur mail"
echo "   Contenu sauvegardé dans : /tmp/email_content_12345.txt"
echo ""

echo "==============================================================================="
echo "Test immédiat :"
echo "==============================================================================="
echo ""
echo "Pour tester immédiatement :"
echo ""
echo "1. Vérifier la commande mail :"
echo "   command -v mail"
echo ""
echo "2. Test d'envoi simple :"
echo "   echo \"Test\" | mail -s \"Test\" <EMAIL>"
echo ""
echo "3. Analyse sans email :"
echo "   ./Check_depordement.ksh VOTRE_FICHIER.log"
echo ""
echo "4. Analyse avec email :"
echo "   export EMAIL_DEST=\"<EMAIL>\""
echo "   ./Check_depordement.ksh VOTRE_FICHIER.log"
echo ""
echo "5. Vérifier la réception de l'email"
echo ""
echo "==============================================================================="
