#!/bin/ksh
#===============================================================================
# Fonctions communes eDSN
#===============================================================================
# Description : Fonctions utilitaires communes à tous les modules eDSN
# Version     : 2.0
#===============================================================================

#===============================================================================
# FONCTIONS DE LOGGING
#===============================================================================

# Fonction de logging avec niveaux
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message"
    
    # Log vers fichier si FICLOG est défini
    if [ -n "$FICLOG" ]; then
        echo "[$timestamp] [$level] $message" >> "$FICLOG"
    fi
}

log_info() {
    log_message "INFO" "$1"
}

log_warn() {
    log_message "WARN" "$1"
}

log_error() {
    log_message "ERROR" "$1"
}

log_debug() {
    if [ "$DEBUG" = "1" ]; then
        log_message "DEBUG" "$1"
    fi
}

#===============================================================================
# FONCTIONS DE VALIDATION
#===============================================================================

# Validation des paramètres d'entrée
validate_parameters() {
    local commande="$1"
    local periode="$2"
    local type_lance="$3"
    local nom_lance="$4"
    
    if [ -z "$commande" ]; then
        log_error "Paramètre Commande manquant"
        show_usage
        return 1
    fi
    
    if [ -z "$periode" ]; then
        log_error "Paramètre Période manquant"
        show_usage
        return 1
    fi
    
    # Validation du format de période (YYYYMM)
    if ! echo "$periode" | grep -q '^[0-9]\{6\}$'; then
        log_error "Format de période invalide: $periode (attendu: YYYYMM)"
        return 1
    fi
    
    return 0
}

# Validation du type de lancement
validate_type_lance() {
    local type_lance="$1"
    
    case "$type_lance" in
        ""|POL|ENT|ETA)
            return 0
            ;;
        *)
            log_error "Type de lancement invalide: $type_lance (attendu: POL, ENT, ETA ou vide)"
            return 1
            ;;
    esac
}

#===============================================================================
# FONCTIONS D'AIDE ET DOCUMENTATION
#===============================================================================

# Affichage de l'aide
show_usage() {
    cat << EOF
Usage: $0 <COMMANDE> <PERIODE> [TYPE_LANCE] [NOM_LANCE] [GESTIONNAIRE]

Paramètres:
  COMMANDE     : Commande eDSN à exécuter (voir liste ci-dessous)
  PERIODE      : Période au format YYYYMM (ex: 202312)
  TYPE_LANCE   : Type de lancement (POL/ENT/ETA, optionnel)
  NOM_LANCE    : Nom du pôle/société/établissement (optionnel)
  GESTIONNAIRE : Nom du gestionnaire (optionnel)

Groupes de commandes disponibles:

CONTRÔLE ET ERREURS:
  CTLWFERR     - Contrôle des processus en erreur
  DELWFERR     - Suppression des processus en erreur
  REPM2MERR    - Reprise des erreurs M2M
  REPM2MERR2   - Reprise avancée des erreurs M2M

GESTION DES CAMPAGNES:
  OUVC         - Ouverture de campagne
  VERCAMP      - Vérification et recréation de campagne
  REOUVCAMP    - Réouverture de campagne
  REINITCAMP   - Réinitialisation de campagne

ALIMENTATION ET GÉNÉRATION:
  ALIM         - Alimentation DSN brouillon
  PREGEN       - Prégénération DSN brouillon
  GENE         - Génération bande DSN brouillon
  GENEDEF      - Génération bande DSN réelle

EXTRACTION ET VALIDATION:
  EXTR         - Extraction DSN mensuelle
  REPEXTR      - Reprise extraction DSN
  CTLV         - Contrôle et validation DSN
  REPCTLV      - Reprise contrôle et validation

ENVOIS:
  ENVP         - Préparation envoi DSN
  ANNENVP      - Annulation envois DSN
  REPENVP      - Reprise préparation envoi
  ENVV         - Validation envois DSN

SIGNALEMENTS:
  SIGSYNCHR0AT - Synchronisation arrêts de travail
  SIGSYNCHR0FCT- Synchronisation fins de contrat
  SIGSYNCHR0RA - Synchronisation reprises arrêt travail

LISTES ET RAPPORTS:
  LSTDECLMENS  - Liste déclarations mensuelles
  LSTDECLCTRL  - Liste déclarations contrôle
  QRYCTRL      - Rapport déclarations contrôle
  QRYMENS      - Rapport déclarations mensuelles

Exemples:
  $0 ALIM 202312 POL "POLE_PARIS"
  $0 CTLWFERR 202312
  $0 LSTDECLMENS 202312 ENT "SOCIETE_A"

Pour plus de détails, consultez la documentation: docs/COMMANDS.md
EOF
}

# Liste des commandes disponibles par groupe
list_commands_by_group() {
    cat << EOF
Commandes eDSN organisées par groupe:

CONTRÔLE ET ERREURS:
  CTLWFERR, DELWFERR, REPM2MERR, REPM2MERR2, VERIFCOMPTE

GESTION DES CAMPAGNES:
  OUVC, VERCAMP, REOUVCAMP, REINITCAMP

ALIMENTATION ET GÉNÉRATION:
  ALIM, PREGEN, GENE, GENEDEF

EXTRACTION ET VALIDATION:
  EXTR, REPEXTR, REPEXTR2, CTLV, REPCTLV

ENVOIS:
  ENVP, ANNENVP, REPENVP, ENVV, VALIDENVOI

SIGNALEMENTS:
  SIGSYNCHR0AT, SIGSYNCHR0FCT, SIGSYNCHR0RA
  SIGSYNCHR0ATOLD, SIGSYNCHR0FCTOLD, SIGSYNCHR0RAOLD
  SIGDOWNATT

LISTES ET RAPPORTS:
  LSTDECLMENS, LSTDECLMENS_CRM, LSTDECLPAS, LSTDECLMENSX
  LSTDECLCTRL, LSTDECLSIG
  QRYCTRL, QRYMENS, QRYMENS331, QRYSIG

PURGE ET MAINTENANCE:
  PURGECTL

PRODUCTION ET CRM:
  PRODCRM, PASM2M, DOWNLOADCRM, RELOADCRM94

INTÉGRATION:
  INTREPVERS

COLLECTE AER:
  SCANAERFCTU*, COLLECTFCTU*, SYNCCRM
EOF
}

#===============================================================================
# FONCTIONS UTILITAIRES
#===============================================================================

# Configuration des options selon le type de lancement
setup_launch_options() {
    local type_lance="$1"
    local nom_lance="$2"
    
    if [ "${type_lance}" = "" ]; then
        TypeLance2=UDS
        NomLance2="*"
    fi
    
    case "${type_lance}" in
        POL)
            Option="-p"
            ;;
        ENT)
            Option="-s"
            ;;
        ETA)
            Option="-e"
            ;;
    esac
    
    export Option TypeLance2 NomLance2
}

# Nettoyage des fichiers temporaires
cleanup_temp_files() {
    local pattern="$1"
    local periode="$2"
    
    if [ -n "$pattern" ] && [ -n "$periode" ]; then
        log_debug "Nettoyage des fichiers temporaires: $TMP/${pattern}*${periode}*"
        rm -f $TMP/${pattern}*${periode}* 2>/dev/null
    fi
}

# Vérification de la taille d'un fichier
check_file_size() {
    local file="$1"
    local min_size="${2:-$TAILLE_FICHIER_MIN}"
    
    if [ -f "$file" ]; then
        local size=$(wc -c < "$file" 2>/dev/null || echo "0")
        [ "$size" -gt "$min_size" ]
    else
        return 1
    fi
}

# Fonction pour attendre et vérifier un processus
wait_and_check_process() {
    local process_file="$1"
    local description="$2"
    
    if [ -s "$process_file" ]; then
        log_info "Attente de la fin du processus: $description"
        LaunchEDSN "dsnworkflow:wait-process @$process_file"
        return $?
    else
        log_warn "Aucun processus à attendre pour: $description"
        return 0
    fi
}
