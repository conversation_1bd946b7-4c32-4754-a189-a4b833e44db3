#!/bin/ksh
#===============================================================================
# Configuration eDSN
#===============================================================================
# Description : Fichier de configuration centralisé pour les scripts eDSN
# Version     : 2.0
#===============================================================================

#===============================================================================
# CONSTANTES DE VERSION eDSN
#===============================================================================
readonly EDSN_VERSION_MIN=220
readonly EDSN_VERSION_NEW=221
readonly EDSN_VERSION_400=400
readonly EDSN_VERSION_350=350

#===============================================================================
# CONSTANTES DE PURGE ET RETENTION
#===============================================================================
readonly NBMOIS_PURGE_DEFAULT=2
readonly TAILLE_FICHIER_MIN=311
readonly TAILLE_FICHIER_CRM_MIN=221

#===============================================================================
# CONSTANTES DE TIMEOUT ET ATTENTE
#===============================================================================
readonly SLEEP_EXTRACTION=300
readonly SLEEP_VALIDATION=5

#===============================================================================
# CONSTANTES DE CODES RETOUR AER
#===============================================================================
readonly AER_CODE_44=44
readonly AER_CODE_94=94

#===============================================================================
# FORMATS DE FICHIERS SUPPORTÉS
#===============================================================================
readonly FORMAT_XLSX="XLSX"
readonly FORMAT_XML="XML"
readonly FORMAT_CSV="CSV"

#===============================================================================
# TYPES DE SIGNALEMENTS
#===============================================================================
readonly SIG_ARRET_TRAVAIL="ARRET_TRAVAIL"
readonly SIG_FIN_CONTRAT="FIN_CONTRAT"
readonly SIG_REPRISE_ARRET_TRAVAIL="REPRISE_ARRET_TRAVAIL"

#===============================================================================
# TYPES DE TÂCHES DSN
#===============================================================================
readonly TASK_TBE="TBE"
readonly TASK_TBV="TBV"
readonly TASK_TBC="TBC"

#===============================================================================
# TYPES DE PROCESSUS
#===============================================================================
readonly PROCESS_MONTHLY="monthlyDeclaration"
readonly PROCESS_M2M="m2m"
readonly PROCESS_M2M_SIG="m2mSignalement"
readonly PROCESS_M2M_ATT="m2mAttachmentDgfipIntegration"
readonly PROCESS_M2M_ATT_INT="m2mAttachmentIntegration"

#===============================================================================
# MODES DSN
#===============================================================================
readonly MODE_TEST="TEST"
readonly MODE_PROD="PROD"

#===============================================================================
# ORGANISMES
#===============================================================================
readonly ORG_NET="NET"
readonly ORG_MSA="MSA"
readonly ORG_DGFIP="DGFIP"

#===============================================================================
# ÉTATS DE CAMPAGNE
#===============================================================================
readonly CAMP_NOTOPEN="NOTOPEN"
readonly CAMP_NOTTESTED="NOTTESTED"

#===============================================================================
# PRÉFIXES DE FICHIERS TEMPORAIRES
#===============================================================================
readonly PREFIX_CTLWFERR="CTLWFERR"
readonly PREFIX_ALIM="ALIM"
readonly PREFIX_PREGEN="PREGEN"
readonly PREFIX_GENE="GENE"
readonly PREFIX_EXTR="EXTR"
readonly PREFIX_ENVP="ENVP"
readonly PREFIX_REPEXTR="REPEXTR"
readonly PREFIX_CTLV="CTLV"

#===============================================================================
# FONCTIONS D'INITIALISATION
#===============================================================================

# Fonction pour valider l'environnement
validate_environment() {
    local required_vars="INIT_PATH TMP UXEXE CONNECT_STRING"
    local missing_vars=""
    
    for var in $required_vars; do
        eval "value=\$$var"
        if [ -z "$value" ]; then
            missing_vars="$missing_vars $var"
        fi
    done
    
    if [ -n "$missing_vars" ]; then
        echo "ERREUR: Variables d'environnement manquantes:$missing_vars" >&2
        return 1
    fi
    
    return 0
}

# Fonction pour initialiser les variables dérivées
init_derived_variables() {
    # Variables de date et temps
    export DATDEB=$(date +'%H:%M:%S')
    export DEBTRT=$(date +'%s')
    export DATJOUR=$(date +'%Y-%m-%d')
    export DATCMPGN=$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")
    
    # Configuration de connexion eDSN
    export EDSN_CONNECT_STRING=${CONNECT_STRING}
    
    if [ "x${APPLI_EDSN_USER}" = "xEDSN" ]; then
        typeset -u L
        typeset -l U
        U=${APPLI_EDSN_USER}
        L=$LOGNAME
        EDSN_CONNECT_STRING=${APPLI_EDSN_USER}/$(psw ${APPLI_EDSN_HOST} $U $L)
    fi
    
    export EDSN_CONNECT_STRING
}

# Fonction pour déterminer la version eDSN et les options
init_edsn_version() {
    GetVarProfile dsnbin
    edsnversion="$(echo $PROFVAR | awk -F"/" '{print $(NF - 1)}' | awk -F"-" '{print $2}' | sed "s:\.::g")"
    [ "$edsnversion" = "" ] && edsnversion=$EDSN_VERSION_MIN
    
    # Initialisation des options selon la version
    [ $edsnversion -gt 210 ] && newcmd="-"
    [ $edsnversion -gt $EDSN_VERSION_NEW ] && newcmd221="-w -1"
    [ $(echo "${edsnversion}" | cut -c1-3) -gt $EDSN_VERSION_400 ] && newcmd400="-n Ctlr-$($UXEXE/uxdat "yyyymmdd" "${S_DATRAIT}" "yyyymmdd")"
    
    export edsnversion newcmd newcmd221 newcmd400
}
