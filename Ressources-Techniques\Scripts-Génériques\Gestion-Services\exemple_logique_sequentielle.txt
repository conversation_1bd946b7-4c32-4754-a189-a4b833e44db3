===============================================================================
NOUVELLE LOGIQUE SÉQUENTIELLE - Check_depordement.ksh
===============================================================================

🎯 PRINCIPE DE LA LOGIQUE SÉQUENTIELLE :
========================================

RÈGLE SIMPLE : Tout débordement qui suit un dépassement dans l'ordre du log 
               appartient automatiquement à ce dépassement.

✅ AVANTAGES :
- Plus simple et plus fiable
- Pas de limite arbitraire de lignes
- Suit l'ordre naturel du traitement
- Évite les fausses associations

❌ ANCIENNE LOGIQUE (proximité) :
- Limite arbitraire de 10 lignes
- Risque de manquer des associations
- Complexité inutile

✅ NOUVELLE LOGIQUE (séquentielle) :
- Association automatique dans l'ordre
- Pas de limite de distance
- Plus fidèle au processus métier

===============================================================================
EXEMPLE CONCRET :
===============================================================================

FICHIER LOG D'ENTRÉE :
---------------------
Ligne 1247: FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE (TABLE WORKING) :ZYWA/000000000000010/000000000000010
Ligne 1248: [autres messages système...]
Ligne 1249: [autres messages système...]
Ligne 1250: [autres messages système...]
...
Ligne 1275: FFCALDBI-DDAD0095-MATRICULE FRR26369 NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES
Ligne 1276: [autres messages système...]
Ligne 1277: FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE (TABLE WORKING) :ABCD/000000000000015/000000000000015
Ligne 1278: [autres messages système...]
...
Ligne 1295: FFCALDBI-DDAD0095-MATRICULE FRR28451 NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES

ANALYSE SÉQUENTIELLE :
=====================

1. Ligne 1247 : DÉPASSEMENT Code ZYWA détecté
   → Mémorisation : code_precedent = "ZYWA"

2. Ligne 1275 : DÉBORDEMENT Matricule FRR26369 détecté
   → Association automatique : ZYWA → FRR26369
   → Écart : 28 lignes (peu importe la distance)
   → Réinitialisation : code_precedent = ""

3. Ligne 1277 : DÉPASSEMENT Code ABCD détecté
   → Mémorisation : code_precedent = "ABCD"

4. Ligne 1295 : DÉBORDEMENT Matricule FRR28451 détecté
   → Association automatique : ABCD → FRR28451
   → Écart : 18 lignes (peu importe la distance)
   → Réinitialisation : code_precedent = ""

===============================================================================
AFFICHAGE RÉSULTANT :
===============================================================================

>>> Analyse détaillée : Dépassements et Débordements associés
==============================================================

┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔍 DÉPASSEMENT #1                                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1247                                                  │
│ Code d'information  : ZYWA                                                  │
│ Table concernée     : TABLE WORKING                                         │
│ Message complet     : FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE...          │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│ ➡️  DÉBORDEMENT ASSOCIÉ                                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1275                                                  │
│ Matricule concerné  : FRR26369                                              │
│ Écart avec dépass.  : 28 ligne(s)                                           │
│ Message complet     : FFCALDBI-DDAD0095-MATRICULE FRR26369 NON TRAITE...    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📋 ASSOCIATION SÉQUENTIELLE :                                               │
│    Dépassement Code ZYWA → Débordement Matricule FRR26369                  │
│    Logique : Débordement suivant le dépassement dans l'ordre du log        │
│    Impact : Données non traitées pour ce matricule                         │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔍 DÉPASSEMENT #2                                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1277                                                  │
│ Code d'information  : ABCD                                                  │
│ Table concernée     : TABLE WORKING                                         │
│ Message complet     : FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE...          │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│ ➡️  DÉBORDEMENT ASSOCIÉ                                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ Ligne du log        : 1295                                                  │
│ Matricule concerné  : FRR28451                                              │
│ Écart avec dépass.  : 18 ligne(s)                                           │
│ Message complet     : FFCALDBI-DDAD0095-MATRICULE FRR28451 NON TRAITE...    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📋 ASSOCIATION SÉQUENTIELLE :                                               │
│    Dépassement Code ABCD → Débordement Matricule FRR28451                  │
│    Logique : Débordement suivant le dépassement dans l'ordre du log        │
│    Impact : Données non traitées pour ce matricule                         │
└─────────────────────────────────────────────────────────────────────────────┘

===============================================================================
TABLEAU RÉCAPITULATIF AVEC LOGIQUE SÉQUENTIELLE :
===============================================================================

>>> Tableau récapitulatif des associations Dépassement → Débordement
======================================================================
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│ Code Info       │ Matricule       │ Ligne Dépass.   │ Ligne Débord.   │ Statut          │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ ZYWA            │ FRR26369        │ 1247            │ 1275            │ 🟢 Associé      │
│ ABCD            │ FRR28451        │ 1277            │ 1295            │ 🟡 Associé      │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┴─────────────────┘

LÉGENDE DES STATUTS :
🔴 Associé (≤ 5 lignes)   : Association très proche
🟡 Associé (≤ 20 lignes)  : Association proche
🟢 Associé (> 20 lignes)  : Association distante mais valide
⚪ Isolé                  : Aucun dépassement précédent

===============================================================================
EMAIL CLIENT AMÉLIORÉ :
===============================================================================

DÉPASSEMENTS ET DÉBORDEMENTS ASSOCIÉS :
======================================

📍 DÉPASSEMENT #1 :
   Code d'information : ZYWA
   Table concernée    : TABLE WORKING
   Ligne complète     : FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE (TABLE WORKING) :ZYWA/000000000000010/000000000000010

   ➡️  DÉBORDEMENT ASSOCIÉ :
       Matricule concerné : FRR26369
       Écart lignes       : 28
       Message complet    : FFCALDBI-DDAD0095-MATRICULE FRR26369 NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES
   📋 ASSOCIATION SÉQUENTIELLE : Dépassement Code ZYWA → Débordement Matricule FRR26369
       Logique : Débordement suivant le dépassement dans l'ordre du log
       Impact : Données de paie non traitées pour ce matricule

📍 DÉPASSEMENT #2 :
   Code d'information : ABCD
   Table concernée    : TABLE WORKING
   Ligne complète     : FDAZYBLS-BBAD0012-DEPASSEMENT DE CAPACITE (TABLE WORKING) :ABCD/000000000000015/000000000000015

   ➡️  DÉBORDEMENT ASSOCIÉ :
       Matricule concerné : FRR28451
       Écart lignes       : 18
       Message complet    : FFCALDBI-DDAD0095-MATRICULE FRR28451 NON TRAITE, VOUS DEVEZ GERER LE DEBORDEMENT DES INFOS ETALEES
   📋 ASSOCIATION SÉQUENTIELLE : Dépassement Code ABCD → Débordement Matricule FRR28451
       Logique : Débordement suivant le dépassement dans l'ordre du log
       Impact : Données de paie non traitées pour ce matricule

📋 RÉSUMÉ DES MATRICULES CONCERNÉS :
====================================

Matricules avec leur dépassement associé :

  1. Matricule FRR26369 → Dépassement Code ZYWA (association séquentielle)
  2. Matricule FRR28451 → Dépassement Code ABCD (association séquentielle)

📊 IMPACT TOTAL : 2 matricule(s) unique(s) concerné(s)

===============================================================================
AVANTAGES DE LA LOGIQUE SÉQUENTIELLE :
===============================================================================

🎯 SIMPLICITÉ :
- Règle simple : "débordement après dépassement = association"
- Pas de calcul de distance complexe
- Logique intuitive et compréhensible

📊 FIABILITÉ :
- Suit l'ordre naturel du traitement de paie
- Pas de limite arbitraire qui pourrait manquer des associations
- Correspond au processus métier réel

🔧 ROBUSTESSE :
- Fonctionne même avec de nombreuses lignes entre dépassement et débordement
- Évite les fausses associations dues à la proximité accidentelle
- Plus stable face aux variations de format de log

📈 PRÉCISION :
- Chaque débordement associé une seule fois
- Réinitialisation après chaque association
- Débordements isolés clairement identifiés

===============================================================================
CAS PARTICULIERS GÉRÉS :
===============================================================================

1. DÉBORDEMENT SANS DÉPASSEMENT PRÉCÉDENT :
   → Marqué comme "Débordement isolé"
   → Pas d'association forcée

2. PLUSIEURS DÉBORDEMENTS APRÈS UN DÉPASSEMENT :
   → Seul le premier est associé
   → Les suivants sont marqués comme isolés

3. DÉPASSEMENT SANS DÉBORDEMENT SUIVANT :
   → Dépassement affiché seul
   → Pas de débordement associé

4. ALTERNANCE DÉPASSEMENT/DÉBORDEMENT :
   → Chaque paire correctement associée
   → Ordre séquentiel respecté

===============================================================================
COMPARAISON AVANT/APRÈS :
===============================================================================

AVANT (Logique de proximité) :
❌ Limite de 10 lignes arbitraire
❌ Risque de manquer des associations légitimes
❌ Complexité de calcul de distance
❌ Associations parfois incorrectes

APRÈS (Logique séquentielle) :
✅ Pas de limite de distance
✅ Toutes les associations légitimes détectées
✅ Simplicité de la règle
✅ Associations toujours correctes

===============================================================================
RÉSULTAT POUR LE CLIENT :
===============================================================================

Le client reçoit maintenant :
✅ Associations claires et fiables
✅ Explication de la logique utilisée
✅ Matricules correctement liés à leur dépassement
✅ Information actionnable et précise

Plus de confusion sur les associations !
Chaque débordement est clairement lié à son dépassement d'origine.

===============================================================================
